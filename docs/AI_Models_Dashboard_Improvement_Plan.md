# AI Models Dashboard - Comprehensive Improvement Plan

This document outlines a comprehensive plan for enhancing the `AIModelsDashboard.tsx` interface, focusing on improved user experience, clearer data presentation, and future extensibility. The goal is to transform the dashboard into a powerful tool for monitoring, managing, and optimizing AI model performance within the Suplementor platform.

## 1. Immediate Priorities (Phase 1)

These are the core requirements identified from the initial request, forming the foundation for further improvements.

### 1.1. Integration of Gemma3 MEDICAL Model

**Objective:** Accurately represent and monitor the Gemma3 MEDICAL model, which operates via Ollama, within the dashboard.

**Proposed Changes in `frontend/src/components/ai/AIModelsDashboard.tsx`:**

*   **`AIModel` Interface Enhancement:**
    *   Extend the `provider` type to include `'ollama'`.
    *   Introduce a new field `modelType: 'general' | 'medical' | 'specialized' | string;` to categorize models.
    *   Add `domain: string[];` to specify the model's primary application areas (e.g., `['medical', 'nutrition']`).
    *   Add `compliance: string[];` for regulatory compliance (e.g., `['HIPAA', 'GDPR']`).

    ```typescript
    interface AIModel {
      id: string;
      name: string;
      provider: 'openai' | 'anthropic' | 'google' | 'local' | 'huggingface' | 'ollama'; // Added 'ollama'
      type: 'chat' | 'completion' | 'embedding' | 'multimodal';
      capabilities: string[];
      maxTokens: number;
      costPerToken: number;
      responseTime: number;
      accuracy: number;
      reliability: number;
      specialties: string[];
      isAvailable: boolean;
      usage: {
        usageCount: number;
        errorCount: number;
        errorRate: number;
        lastUsed: Date;
      };
      performance: {
        averageQuality: number;
        averageConfidence: number;
        averageResponseTime: number;
        requestCount: number;
      };
      modelType: 'general' | 'medical' | 'specialized' | string; // New field
      domain: string[]; // New field
      compliance: string[]; // New field
    }
    ```

*   **Provider Icon and Color Handling:**
    *   Update `getProviderIcon` to include an icon for `'ollama'` (e.g., a custom icon or a generic `Cpu` icon).
    *   Update `getProviderColor` to define a distinct color scheme for `'ollama'` models.

    ```typescript
    const getProviderIcon = (provider: string) => {
      switch (provider) {
        case 'openai': return <Brain className="w-5 h-5 text-green-600" />;
        case 'anthropic': return <Shield className="w-5 h-5 text-blue-600" />;
        case 'google': return <Globe className="w-5 h-5 text-red-600" />;
        case 'local': return <Home className="w-5 h-5 text-purple-600" />;
        case 'ollama': return <Cpu className="w-5 h-5 text-orange-600" />; // Example icon and color
        default: return <Cpu className="w-5 h-5 text-gray-600" />;
      }
    };

    const getProviderColor = (provider: string) => {
      switch (provider) {
        case 'openai': return 'from-green-500 to-emerald-600';
        case 'anthropic': return 'from-blue-500 to-indigo-600';
        case 'google': return 'from-red-500 to-pink-600';
        case 'local': return 'from-purple-500 to-violet-600';
        case 'ollama': return 'from-orange-500 to-amber-600'; // Example color scheme
        default: return 'from-gray-500 to-slate-600';
      }
    };
    ```

*   **Displaying Specialized Model Details:**
    *   Within the model card, display the `modelType`, `domain`, and `compliance` information, especially for models like Gemma3 MEDICAL. This could be part of the expanded details section.

**Backend Assumption:** The backend API (`/api/enhanced-ai/models`) is assumed to be updated to provide the `provider: 'ollama'`, `modelType: 'medical'`, `domain: ['medical', 'nutrition']`, and `compliance: ['HIPAA']` (or similar) for the Gemma3 MEDICAL model.

### 1.2. AG-UI Protocol Status Display

**Objective:** Provide real-time visibility into the connection status of the AG-UI protocol, which is crucial for real-time agent communication.

**Proposed Changes in `frontend/src/components/ai/AIModelsDashboard.tsx`:**

*   **Integration of `useAGUIClient` Hook:**
    *   Import and utilize the `useAGUIClient` hook to access `isConnected` and `connectionStatus`.

    ```typescript
    import { useAGUIClient } from '../../hooks/useAGUIClient';
    // ... inside AIModelsDashboard component ...
    const { isConnected, connectionStatus } = useAGUIClient();
    ```

*   **Dedicated AG-UI Status Card/Section:**
    *   Add a new card or a dedicated section within the dashboard, perhaps near the "Real-time Status" section, to display the AG-UI connection status.
    *   Visually represent the status (e.g., green for connected, red for disconnected, yellow for connecting/reconnecting).

    ```html
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.4 }}
      className="bg-white rounded-xl shadow-lg p-6"
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">AG-UI Protocol Status</h3>
        <div className="flex items-center space-x-2 text-sm">
          <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'} ${connectionStatus === 'Connecting...' ? 'animate-pulse bg-yellow-500' : ''}`}></div>
          <span className="text-gray-600">{connectionStatus}</span>
        </div>
      </div>
      <p className="text-sm text-gray-500">Real-time communication channel for agent events and graph updates.</p>
    </motion.div>
    ```

## 2. Comprehensive Dashboard Enhancements (Phase 2 & Beyond)

This section outlines broader improvements to elevate the AI Models Dashboard.

### 2.1. Enhanced Data Visualization & Metrics

*   **Historical Performance Trends:**
    *   Implement interactive charts (e.g., using Recharts or Nivo) to visualize usage, error rates, response times, and quality scores over time (daily, weekly, monthly).
    *   Allow users to select time ranges for historical data.
*   **Comparative Analysis:**
    *   Enable side-by-side comparison of selected AI models based on key metrics (cost, quality, response time).
    *   Visual heatmaps or radar charts for multi-dimensional comparison.
*   **Granular Cost Breakdown:**
    *   Display cost per request, cost per token, and total estimated cost over selected periods.
    *   Break down costs by model, provider, and application area.
*   **Throughput and Latency Distribution:**
    *   Visualize requests per second (RPS) and latency distribution (e.g., p90, p95, p99 latencies) to identify bottlenecks.
*   **Dynamic Alerting:**
    *   Allow users to set custom thresholds for metrics (e.g., error rate, response time) and receive in-dashboard notifications or external alerts (e.g., email, Slack - *requires backend integration*).

### 2.2. Advanced Filtering, Sorting, and Search

*   **Multi-faceted Filtering:**
    *   Implement robust filtering options by:
        *   **Provider:** (OpenAI, Google, Anthropic, Ollama, Local, HuggingFace)
        *   **Model Type:** (General, Medical, Specialized, Chat, Completion, Embedding, Multimodal)
        *   **Capabilities:** (e.g., `['text generation', 'image analysis', 'code generation']`)
        *   **Domain:** (e.g., `['medical', 'finance', 'legal']`)
        *   **Availability:** (Online, Offline)
        *   **Performance Metrics:** (e.g., filter by models with quality > 80%, response time < 500ms)
    *   **Search Bar:** A global search bar to quickly find models by name, ID, or keyword.
*   **Customizable Sorting:**
    *   Sort models by any displayed metric (e.g., `name`, `costPerToken`, `averageQuality`, `lastUsed`).
*   **Saved Views/Presets:**
    *   Allow users to save their preferred filter and sort combinations as custom views for quick access.

### 2.3. Model Management & Actions (Requires Backend Integration)

*   **Enable/Disable Models:**
    *   Toggle model availability directly from the dashboard (requires API endpoint).
*   **Model Versioning:**
    *   Display model versions and allow switching between them for testing or deployment.
*   **Deployment Status:**
    *   Indicate the deployment status of local or custom models (e.g., `deploying`, `running`, `failed`).
*   **Retraining/Fine-tuning Triggers:**
    *   Provide buttons to trigger retraining or fine-tuning processes for specific models (requires backend orchestration).
*   **Configuration Editor:**
    *   A modal or dedicated view to edit model-specific configurations (e.g., API keys, temperature, max tokens - *sensitive data handling required*).

### 2.4. User Experience (UX) & Interface Design

*   **Responsive Layout:**
    *   Ensure optimal display and usability across various devices (desktop, tablet, mobile).
*   **Dark Mode:**
    *   Implement a toggle for a dark theme to reduce eye strain and improve aesthetics.
*   **Intuitive Navigation:**
    *   Clear breadcrumbs or tabs if the dashboard grows into multiple sub-sections.
*   **Tooltips and Contextual Help:**
    *   Provide informative tooltips for all metrics, icons, and interactive elements.
*   **Visual Consistency:**
    *   Adhere to a consistent design system (e.g., Tailwind CSS components used in the project) for all new elements.
*   **Accessibility (WCAG Compliance):**
    *   Ensure all interactive elements are keyboard-navigable, have appropriate ARIA attributes, and sufficient color contrast.
*   **Loading States & Skeletons:**
    *   Implement more sophisticated loading indicators and skeleton screens for a smoother user experience during data fetching.

### 2.5. Advanced Features & Future Considerations

*   **Model A/B Testing:**
    *   Tools to set up and monitor A/B tests for different model versions or configurations.
*   **Automated Model Routing/Orchestration:**
    *   A module to define rules for automatically routing requests to the best-performing or most cost-effective model based on real-time metrics.
*   **Explainable AI (XAI) Insights:**
    *   For supported models, display insights into why a model made a particular decision (e.g., feature importance, confidence scores for specific outputs).
*   **Security & Access Control:**
    *   Integrate with user roles and permissions to control who can view or manage specific models.
*   **Integration with External Monitoring:**
    *   Provide webhooks or API endpoints to push dashboard metrics to external monitoring systems (e.g., Prometheus, Grafana).
*   **Model Marketplace/Discovery:**
    *   If applicable, a section to discover and integrate new pre-trained or custom models.

## 3. Implementation Roadmap (High-Level)

```mermaid
graph TD
    A[Phase 1: Immediate Priorities] --> A1[Integrate Gemma3 MEDICAL];
    A1 --> A2[Display AG-UI Status];
    A2 --> A3[Refine AIModel Interface];

    A[Phase 1] --> B[Phase 2: Core Enhancements];
    B --> B1[Enhanced Data Visualization];
    B1 --> B2[Advanced Filtering/Sorting];
    B2 --> B3[Basic Model Management Actions];

    B[Phase 2] --> C[Phase 3: Advanced Features & UX];
    C --> C1[Historical Trends & Comparative Analysis];
    C1 --> C2[Full Model Management (Versioning, Retraining)];
    C2 --> C3[Advanced UX/UI (Dark Mode, Responsiveness)];
    C3 --> C4[Dynamic Alerting];

    C[Phase 3] --> D[Phase 4: Future Considerations];
    D --> D1[Model A/B Testing];
    D1 --> D2[Automated Routing];
    D2 --> D3[XAI Insights];
    D3 --> D4[Security & Access Control];
```

This comprehensive plan addresses the user's request for a broader perspective on improving the AI Models Dashboard, incorporating the immediate needs for Gemma3 MEDICAL and AG-UI integration within a scalable framework.