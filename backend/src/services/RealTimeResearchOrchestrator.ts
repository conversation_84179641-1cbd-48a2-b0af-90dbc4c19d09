import { EventEmitter } from 'events';
import { logger } from '../utils/logger';
import { performanceMonitoringService } from './PerformanceMonitoringService';
import { enhancedCacheService } from './EnhancedCacheService';
import { MCPResearchOrchestrator } from './MCPResearchOrchestrator';
import { ResearchAIService } from './ResearchAIService';

export interface ResearchTask {
  id: string;
  sessionId: string;
  type: 'search' | 'analysis' | 'synthesis' | 'validation' | 'enhancement';
  query: string;
  parameters: Record<string, any>;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  startTime?: Date;
  endTime?: Date;
  results?: any;
  error?: string;
  dependencies: string[]; // Task IDs this task depends on
  estimatedDuration: number; // in milliseconds
  actualDuration?: number;
}

export interface ResearchSession {
  id: string;
  userId: string;
  roomId?: string;
  title: string;
  description?: string;
  type: 'autonomous' | 'guided' | 'collaborative';
  status: 'initializing' | 'running' | 'paused' | 'completed' | 'failed';
  tasks: Map<string, ResearchTask>;
  taskQueue: string[]; // Task IDs in execution order
  currentTask?: string;
  results: Map<string, any>;
  metadata: {
    totalTasks: number;
    completedTasks: number;
    failedTasks: number;
    estimatedCompletion: Date;
    actualCompletion?: Date;
    qualityScore?: number;
    confidence?: number;
  };
  settings: {
    maxConcurrentTasks: number;
    autoRetry: boolean;
    retryAttempts: number;
    timeoutMs: number;
    qualityThreshold: number;
    enableRealTimeUpdates: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface ResearchAgent {
  id: string;
  name: string;
  type: 'search' | 'analysis' | 'synthesis' | 'validation';
  status: 'idle' | 'busy' | 'error' | 'offline';
  capabilities: string[];
  currentTask?: string;
  performance: {
    tasksCompleted: number;
    averageTime: number;
    successRate: number;
    qualityScore: number;
  };
  lastActivity: Date;
}

/**
 * Real-time research orchestrator with advanced task management and live updates
 */
export class RealTimeResearchOrchestrator extends EventEmitter {
  private sessions: Map<string, ResearchSession> = new Map();
  private agents: Map<string, ResearchAgent> = new Map();
  private taskExecutors: Map<string, Promise<any>> = new Map();
  private mcpOrchestrator: MCPResearchOrchestrator;
  private aiService: ResearchAIService;
  private isRunning: boolean = false;

  constructor(mcpOrchestrator: MCPResearchOrchestrator, aiService: ResearchAIService) {
    super();
    this.mcpOrchestrator = mcpOrchestrator;
    this.aiService = aiService;
    this.initializeAgents();
    this.startOrchestrator();
    logger.info('🤖 Real-Time Research Orchestrator initialized');
  }

  /**
   * Initialize research agents
   */
  private initializeAgents(): void {
    const agents: Omit<ResearchAgent, 'id'>[] = [
      {
        name: 'Search Agent Alpha',
        type: 'search',
        status: 'idle',
        capabilities: ['web_search', 'academic_search', 'news_search', 'social_search'],
        performance: { tasksCompleted: 0, averageTime: 0, successRate: 100, qualityScore: 85 },
        lastActivity: new Date()
      },
      {
        name: 'Analysis Agent Beta',
        type: 'analysis',
        status: 'idle',
        capabilities: ['content_analysis', 'sentiment_analysis', 'trend_analysis', 'statistical_analysis'],
        performance: { tasksCompleted: 0, averageTime: 0, successRate: 100, qualityScore: 90 },
        lastActivity: new Date()
      },
      {
        name: 'Synthesis Agent Gamma',
        type: 'synthesis',
        status: 'idle',
        capabilities: ['data_synthesis', 'report_generation', 'insight_extraction', 'pattern_recognition'],
        performance: { tasksCompleted: 0, averageTime: 0, successRate: 100, qualityScore: 88 },
        lastActivity: new Date()
      },
      {
        name: 'Validation Agent Delta',
        type: 'validation',
        status: 'idle',
        capabilities: ['fact_checking', 'source_verification', 'quality_assessment', 'bias_detection'],
        performance: { tasksCompleted: 0, averageTime: 0, successRate: 100, qualityScore: 92 },
        lastActivity: new Date()
      }
    ];

    agents.forEach((agent, index) => {
      const agentId = `agent_${agent.type}_${index + 1}`;
      this.agents.set(agentId, { ...agent, id: agentId });
    });
  }

  /**
   * Start the orchestrator main loop
   */
  private startOrchestrator(): void {
    this.isRunning = true;
    this.orchestratorLoop();
  }

  /**
   * Main orchestrator loop
   */
  private async orchestratorLoop(): Promise<void> {
    while (this.isRunning) {
      try {
        // Process all active sessions
        for (const session of this.sessions.values()) {
          if (session.status === 'running') {
            await this.processSession(session);
          }
        }

        // Update agent statuses
        this.updateAgentStatuses();

        // Emit orchestrator heartbeat
        this.emit('orchestrator_heartbeat', {
          activeSessions: this.getActiveSessions().length,
          busyAgents: this.getBusyAgents().length,
          queuedTasks: this.getQueuedTasks().length,
          timestamp: new Date()
        });

        // Wait before next iteration
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        logger.error('Orchestrator loop error:', error);
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
  }

  /**
   * Create a new research session
   */
  public async createSession(params: {
    userId: string;
    roomId?: string;
    title: string;
    description?: string;
    type: ResearchSession['type'];
    query: string;
    settings?: Partial<ResearchSession['settings']>;
  }): Promise<string> {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const session: ResearchSession = {
      id: sessionId,
      userId: params.userId,
      roomId: params.roomId,
      title: params.title,
      description: params.description,
      type: params.type,
      status: 'initializing',
      tasks: new Map(),
      taskQueue: [],
      results: new Map(),
      metadata: {
        totalTasks: 0,
        completedTasks: 0,
        failedTasks: 0,
        estimatedCompletion: new Date(Date.now() + 300000) // 5 minutes default
      },
      settings: {
        maxConcurrentTasks: 3,
        autoRetry: true,
        retryAttempts: 2,
        timeoutMs: 120000, // 2 minutes
        qualityThreshold: 0.7,
        enableRealTimeUpdates: true,
        ...params.settings
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.sessions.set(sessionId, session);

    // Generate initial tasks based on query and type
    await this.generateInitialTasks(session, params.query);

    // Start the session
    session.status = 'running';
    session.updatedAt = new Date();

    this.emit('session_created', { sessionId, session: this.serializeSession(session) });
    logger.info(`🚀 Research session created: ${sessionId} for user ${params.userId}`);

    return sessionId;
  }

  /**
   * Generate initial tasks for a session
   */
  private async generateInitialTasks(session: ResearchSession, query: string): Promise<void> {
    const metricId = performanceMonitoringService.trackAIOperation('generate_tasks', 'task_planning');

    try {
      const taskPlan = await this.aiService.generateTaskPlan(query, session.type);
      
      taskPlan.tasks.forEach((taskData, index) => {
        const taskId = `task_${session.id}_${index + 1}`;
        const task: ResearchTask = {
          id: taskId,
          sessionId: session.id,
          type: taskData.type,
          query: taskData.query,
          parameters: taskData.parameters || {},
          priority: taskData.priority || 'medium',
          status: 'pending',
          progress: 0,
          dependencies: taskData.dependencies || [],
          estimatedDuration: taskData.estimatedDuration || 60000 // 1 minute default
        };

        session.tasks.set(taskId, task);
        session.taskQueue.push(taskId);
      });

      session.metadata.totalTasks = session.tasks.size;
      session.metadata.estimatedCompletion = new Date(
        Date.now() + Array.from(session.tasks.values()).reduce((sum, task) => sum + task.estimatedDuration, 0)
      );

      performanceMonitoringService.endMetric(metricId, true);
    } catch (error) {
      performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Task generation failed');
      throw error;
    }
  }

  /**
   * Process a research session
   */
  private async processSession(session: ResearchSession): Promise<void> {
    // Check if we can start new tasks
    const runningTasks = Array.from(session.tasks.values()).filter(task => task.status === 'running');
    const availableSlots = session.settings.maxConcurrentTasks - runningTasks.length;

    if (availableSlots > 0) {
      // Find next tasks to execute
      const readyTasks = this.getReadyTasks(session);
      const tasksToStart = readyTasks.slice(0, availableSlots);

      for (const task of tasksToStart) {
        await this.startTask(session, task);
      }
    }

    // Check if session is complete
    const allTasks = Array.from(session.tasks.values());
    const completedTasks = allTasks.filter(task => task.status === 'completed');
    const failedTasks = allTasks.filter(task => task.status === 'failed');

    if (completedTasks.length + failedTasks.length === allTasks.length) {
      await this.completeSession(session);
    }

    // Update session metadata
    session.metadata.completedTasks = completedTasks.length;
    session.metadata.failedTasks = failedTasks.length;
    session.updatedAt = new Date();

    // Emit progress update
    if (session.settings.enableRealTimeUpdates) {
      this.emit('session_progress', {
        sessionId: session.id,
        progress: this.calculateSessionProgress(session),
        metadata: session.metadata,
        currentTasks: runningTasks.map(task => ({
          id: task.id,
          type: task.type,
          progress: task.progress,
          query: task.query
        }))
      });
    }
  }

  /**
   * Get tasks that are ready to execute
   */
  private getReadyTasks(session: ResearchSession): ResearchTask[] {
    return Array.from(session.tasks.values())
      .filter(task => {
        if (task.status !== 'pending') return false;
        
        // Check if all dependencies are completed
        return task.dependencies.every(depId => {
          const depTask = session.tasks.get(depId);
          return depTask && depTask.status === 'completed';
        });
      })
      .sort((a, b) => {
        // Sort by priority
        const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      });
  }

  /**
   * Start executing a task
   */
  private async startTask(session: ResearchSession, task: ResearchTask): Promise<void> {
    // Find available agent
    const agent = this.findAvailableAgent(task.type);
    if (!agent) {
      logger.warn(`No available agent for task type: ${task.type}`);
      return;
    }

    // Update task and agent status
    task.status = 'running';
    task.startTime = new Date();
    task.progress = 0;
    agent.status = 'busy';
    agent.currentTask = task.id;
    agent.lastActivity = new Date();

    this.emit('task_started', {
      sessionId: session.id,
      taskId: task.id,
      agentId: agent.id,
      task: this.serializeTask(task)
    });

    // Execute task
    const executor = this.executeTask(session, task, agent);
    this.taskExecutors.set(task.id, executor);

    try {
      const result = await executor;
      await this.completeTask(session, task, agent, result);
    } catch (error) {
      await this.failTask(session, task, agent, error);
    } finally {
      this.taskExecutors.delete(task.id);
    }
  }

  /**
   * Execute a task with an agent
   */
  private async executeTask(session: ResearchSession, task: ResearchTask, agent: ResearchAgent): Promise<any> {
    const metricId = performanceMonitoringService.trackAIOperation(`execute_${task.type}`, agent.name);

    try {
      let result;

      // Progress updates during execution
      const progressInterval = setInterval(() => {
        if (task.status === 'running') {
          task.progress = Math.min(task.progress + 10, 90); // Simulate progress
          this.emit('task_progress', {
            sessionId: session.id,
            taskId: task.id,
            progress: task.progress
          });
        }
      }, 2000);

      try {
        switch (task.type) {
          case 'search':
            result = await this.mcpOrchestrator.performComprehensiveResearch(task.query, {
              maxResults: task.parameters.maxResults || 20,
              includeAnalysis: task.parameters.includeAnalysis || true
            });
            break;

          case 'analysis':
            result = await this.aiService.analyzeContent(task.parameters.content, {
              analysisType: task.parameters.analysisType || 'comprehensive',
              includeInsights: true
            });
            break;

          case 'synthesis':
            result = await this.aiService.synthesizeResults(task.parameters.data, {
              format: task.parameters.format || 'report',
              includeRecommendations: true
            });
            break;

          case 'validation':
            result = await this.aiService.validateResults(task.parameters.results, {
              checkSources: true,
              assessQuality: true
            });
            break;

          default:
            throw new Error(`Unknown task type: ${task.type}`);
        }

        clearInterval(progressInterval);
        task.progress = 100;

        performanceMonitoringService.endMetric(metricId, true);
        return result;
      } finally {
        clearInterval(progressInterval);
      }
    } catch (error) {
      performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Task execution failed');
      throw error;
    }
  }

  /**
   * Complete a task successfully
   */
  private async completeTask(session: ResearchSession, task: ResearchTask, agent: ResearchAgent, result: any): Promise<void> {
    task.status = 'completed';
    task.endTime = new Date();
    task.actualDuration = task.endTime.getTime() - task.startTime!.getTime();
    task.results = result;
    task.progress = 100;

    // Update agent
    agent.status = 'idle';
    agent.currentTask = undefined;
    agent.performance.tasksCompleted++;
    agent.performance.averageTime = (
      (agent.performance.averageTime * (agent.performance.tasksCompleted - 1) + task.actualDuration) /
      agent.performance.tasksCompleted
    );

    // Store results
    session.results.set(task.id, result);

    // Cache results for future use
    await enhancedCacheService.set(
      `task_result:${task.id}`,
      result,
      { ttl: 3600, tags: ['task_results', session.id] }
    );

    this.emit('task_completed', {
      sessionId: session.id,
      taskId: task.id,
      agentId: agent.id,
      result,
      duration: task.actualDuration
    });

    logger.info(`✅ Task completed: ${task.id} by ${agent.name} in ${task.actualDuration}ms`);
  }

  /**
   * Fail a task
   */
  private async failTask(session: ResearchSession, task: ResearchTask, agent: ResearchAgent, error: any): Promise<void> {
    task.status = 'failed';
    task.endTime = new Date();
    task.actualDuration = task.endTime.getTime() - task.startTime!.getTime();
    task.error = error instanceof Error ? error.message : String(error);

    // Update agent
    agent.status = 'idle';
    agent.currentTask = undefined;
    agent.performance.successRate = Math.max(0, agent.performance.successRate - 5);

    this.emit('task_failed', {
      sessionId: session.id,
      taskId: task.id,
      agentId: agent.id,
      error: task.error,
      duration: task.actualDuration
    });

    logger.error(`❌ Task failed: ${task.id} by ${agent.name} - ${task.error}`);

    // Retry if enabled
    if (session.settings.autoRetry && (task.parameters.retryCount || 0) < session.settings.retryAttempts) {
      setTimeout(() => {
        task.status = 'pending';
        task.parameters.retryCount = (task.parameters.retryCount || 0) + 1;
        logger.info(`🔄 Retrying task: ${task.id} (attempt ${task.parameters.retryCount})`);
      }, 5000);
    }
  }

  /**
   * Complete a research session
   */
  private async completeSession(session: ResearchSession): Promise<void> {
    session.status = 'completed';
    session.metadata.actualCompletion = new Date();
    session.updatedAt = new Date();

    // Calculate quality score
    const results = Array.from(session.results.values());
    session.metadata.qualityScore = await this.calculateSessionQuality(results);
    session.metadata.confidence = await this.calculateSessionConfidence(session);

    this.emit('session_completed', {
      sessionId: session.id,
      session: this.serializeSession(session),
      summary: {
        totalTasks: session.metadata.totalTasks,
        completedTasks: session.metadata.completedTasks,
        failedTasks: session.metadata.failedTasks,
        qualityScore: session.metadata.qualityScore,
        confidence: session.metadata.confidence,
        duration: session.metadata.actualCompletion.getTime() - session.createdAt.getTime()
      }
    });

    logger.info(`🎉 Research session completed: ${session.id} with quality score ${session.metadata.qualityScore}`);
  }

  // Helper methods
  private findAvailableAgent(taskType: ResearchTask['type']): ResearchAgent | null {
    return Array.from(this.agents.values()).find(agent => 
      agent.type === taskType && agent.status === 'idle'
    ) || null;
  }

  private calculateSessionProgress(session: ResearchSession): number {
    const totalTasks = session.metadata.totalTasks;
    if (totalTasks === 0) return 0;

    const taskProgress = Array.from(session.tasks.values()).reduce((sum, task) => {
      return sum + (task.progress / 100);
    }, 0);

    return Math.round((taskProgress / totalTasks) * 100);
  }

  private async calculateSessionQuality(results: any[]): Promise<number> {
    // Simplified quality calculation
    if (results.length === 0) return 0;
    
    const qualityScores = results.map(result => {
      if (result.quality) return result.quality;
      if (result.confidence) return result.confidence;
      return 0.8; // Default quality
    });

    return Math.round(qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length * 100);
  }

  private async calculateSessionConfidence(session: ResearchSession): Promise<number> {
    const completionRate = session.metadata.completedTasks / session.metadata.totalTasks;
    const failureRate = session.metadata.failedTasks / session.metadata.totalTasks;
    
    return Math.round((completionRate * 0.7 + (1 - failureRate) * 0.3) * 100);
  }

  private updateAgentStatuses(): void {
    const now = new Date();
    for (const agent of this.agents.values()) {
      // Mark agents as offline if no activity for 5 minutes
      if (now.getTime() - agent.lastActivity.getTime() > 300000) {
        agent.status = 'offline';
      }
    }
  }

  private serializeSession(session: ResearchSession): any {
    return {
      ...session,
      tasks: Array.from(session.tasks.values()).map(task => this.serializeTask(task)),
      results: Object.fromEntries(session.results)
    };
  }

  private serializeTask(task: ResearchTask): any {
    return { ...task };
  }

  // Public API methods
  public getSession(sessionId: string): ResearchSession | null {
    return this.sessions.get(sessionId) || null;
  }

  public getActiveSessions(): ResearchSession[] {
    return Array.from(this.sessions.values()).filter(session => 
      session.status === 'running' || session.status === 'initializing'
    );
  }

  public getBusyAgents(): ResearchAgent[] {
    return Array.from(this.agents.values()).filter(agent => agent.status === 'busy');
  }

  public getQueuedTasks(): ResearchTask[] {
    const queuedTasks: ResearchTask[] = [];
    for (const session of this.sessions.values()) {
      if (session.status === 'running') {
        queuedTasks.push(...Array.from(session.tasks.values()).filter(task => task.status === 'pending'));
      }
    }
    return queuedTasks;
  }

  public async pauseSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (session && session.status === 'running') {
      session.status = 'paused';
      session.updatedAt = new Date();
      this.emit('session_paused', { sessionId });
    }
  }

  public async resumeSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (session && session.status === 'paused') {
      session.status = 'running';
      session.updatedAt = new Date();
      this.emit('session_resumed', { sessionId });
    }
  }

  public async cancelSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (session) {
      // Cancel running tasks
      for (const task of session.tasks.values()) {
        if (task.status === 'running') {
          task.status = 'cancelled';
          const executor = this.taskExecutors.get(task.id);
          if (executor) {
            // Note: In a real implementation, you'd want to implement proper cancellation
            this.taskExecutors.delete(task.id);
          }
        }
      }

      session.status = 'failed';
      session.updatedAt = new Date();
      this.emit('session_cancelled', { sessionId });
    }
  }

  public getAgents(): ResearchAgent[] {
    return Array.from(this.agents.values());
  }

  public getAgentPerformance(): Record<string, any> {
    const agents = Array.from(this.agents.values());
    return {
      totalAgents: agents.length,
      activeAgents: agents.filter(a => a.status !== 'offline').length,
      busyAgents: agents.filter(a => a.status === 'busy').length,
      averagePerformance: {
        tasksCompleted: agents.reduce((sum, a) => sum + a.performance.tasksCompleted, 0),
        averageTime: agents.reduce((sum, a) => sum + a.performance.averageTime, 0) / agents.length,
        successRate: agents.reduce((sum, a) => sum + a.performance.successRate, 0) / agents.length,
        qualityScore: agents.reduce((sum, a) => sum + a.performance.qualityScore, 0) / agents.length
      }
    };
  }

  public shutdown(): void {
    this.isRunning = false;
    logger.info('🛑 Real-Time Research Orchestrator shutdown');
  }
}

export default RealTimeResearchOrchestrator;
