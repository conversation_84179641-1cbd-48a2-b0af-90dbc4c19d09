import { Server as SocketIOServer, Socket } from 'socket.io';
import { Server as HttpServer } from 'http';
import { logger } from '../utils/logger';
import { performanceMonitoringService } from './PerformanceMonitoringService';
import { enhancedCacheService } from './EnhancedCacheService';

export interface UserPresence {
  userId: string;
  username: string;
  avatar?: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  lastSeen: Date;
  currentRoom?: string;
  activeResearch?: string;
}

export interface ResearchRoom {
  id: string;
  name: string;
  description?: string;
  type: 'research' | 'collaboration' | 'analysis';
  createdBy: string;
  createdAt: Date;
  participants: Set<string>;
  isPrivate: boolean;
  maxParticipants?: number;
  activeResearch?: string;
  settings: {
    allowComments: boolean;
    allowEditing: boolean;
    autoSave: boolean;
    notifications: boolean;
  };
}

export interface ResearchProgress {
  sessionId: string;
  roomId?: string;
  userId: string;
  type: 'search' | 'analysis' | 'synthesis' | 'validation';
  status: 'started' | 'in_progress' | 'completed' | 'error';
  progress: number; // 0-100
  currentStep: string;
  totalSteps: number;
  results?: any;
  error?: string;
  timestamp: Date;
}

export interface LiveNotification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'research_update';
  title: string;
  message: string;
  userId?: string;
  roomId?: string;
  data?: any;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  persistent: boolean;
  timestamp: Date;
  expiresAt?: Date;
}

export interface RoomMessage {
  id: string;
  roomId: string;
  userId: string;
  username: string;
  type: 'text' | 'research_result' | 'system' | 'file' | 'reaction';
  content: string;
  data?: any;
  replyTo?: string;
  reactions: Map<string, string[]>; // emoji -> userIds
  timestamp: Date;
  edited?: Date;
}

/**
 * Enhanced WebSocket service with multi-room support and real-time collaboration
 */
export class EnhancedWebSocketService {
  private io: SocketIOServer;
  private connectedUsers: Map<string, UserPresence> = new Map();
  private userSockets: Map<string, Set<string>> = new Map(); // userId -> socketIds
  private socketUsers: Map<string, string> = new Map(); // socketId -> userId
  private researchRooms: Map<string, ResearchRoom> = new Map();
  private roomMessages: Map<string, RoomMessage[]> = new Map();
  private activeResearchSessions: Map<string, ResearchProgress> = new Map();
  private notifications: Map<string, LiveNotification[]> = new Map(); // userId -> notifications

  constructor(server: HttpServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:5175",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling'],
      pingTimeout: 60000,
      pingInterval: 25000
    });

    this.initializeEventHandlers();
    this.startCleanupTasks();
    logger.info('🚀 Enhanced WebSocket Service initialized with multi-room support');
  }

  /**
   * Initialize all WebSocket event handlers
   */
  private initializeEventHandlers(): void {
    this.io.on('connection', (socket: Socket) => {
      logger.info(`🔌 New WebSocket connection: ${socket.id}`);

      // Authentication and user setup
      socket.on('authenticate', (data: { userId: string; username: string; avatar?: string }) => {
        this.handleUserAuthentication(socket, data);
      });

      // Room management
      socket.on('join_room', (data: { roomId: string; password?: string }) => {
        this.handleJoinRoom(socket, data);
      });

      socket.on('leave_room', (data: { roomId: string }) => {
        this.handleLeaveRoom(socket, data);
      });

      socket.on('create_room', (data: Partial<ResearchRoom>) => {
        this.handleCreateRoom(socket, data);
      });

      // Research progress
      socket.on('start_research', (data: { type: string; query: string; roomId?: string }) => {
        this.handleStartResearch(socket, data);
      });

      socket.on('research_progress', (data: Partial<ResearchProgress>) => {
        this.handleResearchProgress(socket, data);
      });

      // Messaging
      socket.on('send_message', (data: { roomId: string; content: string; type?: string; replyTo?: string }) => {
        this.handleSendMessage(socket, data);
      });

      socket.on('add_reaction', (data: { messageId: string; emoji: string }) => {
        this.handleAddReaction(socket, data);
      });

      // Presence and status
      socket.on('update_status', (data: { status: UserPresence['status'] }) => {
        this.handleUpdateStatus(socket, data);
      });

      socket.on('typing_start', (data: { roomId: string }) => {
        this.handleTypingStart(socket, data);
      });

      socket.on('typing_stop', (data: { roomId: string }) => {
        this.handleTypingStop(socket, data);
      });

      // Notifications
      socket.on('mark_notification_read', (data: { notificationId: string }) => {
        this.handleMarkNotificationRead(socket, data);
      });

      // Disconnect handling
      socket.on('disconnect', (reason) => {
        this.handleDisconnect(socket, reason);
      });

      // Error handling
      socket.on('error', (error) => {
        logger.error('WebSocket error:', error, { socketId: socket.id });
      });
    });
  }

  /**
   * Handle user authentication
   */
  private handleUserAuthentication(socket: Socket, data: { userId: string; username: string; avatar?: string }): void {
    const metricId = performanceMonitoringService.trackApiRequest('websocket_auth', 'POST');
    
    try {
      const { userId, username, avatar } = data;
      
      // Update user presence
      const userPresence: UserPresence = {
        userId,
        username,
        avatar,
        status: 'online',
        lastSeen: new Date()
      };

      this.connectedUsers.set(userId, userPresence);
      
      // Track user socket mapping
      if (!this.userSockets.has(userId)) {
        this.userSockets.set(userId, new Set());
      }
      this.userSockets.get(userId)!.add(socket.id);
      this.socketUsers.set(socket.id, userId);

      // Join user to their personal notification room
      socket.join(`user:${userId}`);

      // Send authentication success
      socket.emit('authenticated', {
        success: true,
        user: userPresence,
        rooms: this.getUserRooms(userId),
        notifications: this.getUserNotifications(userId)
      });

      // Broadcast user online status
      this.broadcastUserPresence(userId, 'online');

      performanceMonitoringService.endMetric(metricId, true);
      logger.info(`✅ User authenticated: ${username} (${userId})`);
    } catch (error) {
      performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Auth failed');
      socket.emit('authentication_error', { error: 'Authentication failed' });
      logger.error('Authentication error:', error);
    }
  }

  /**
   * Handle joining a room
   */
  private handleJoinRoom(socket: Socket, data: { roomId: string; password?: string }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) {
      socket.emit('error', { message: 'Not authenticated' });
      return;
    }

    const room = this.researchRooms.get(data.roomId);
    if (!room) {
      socket.emit('error', { message: 'Room not found' });
      return;
    }

    // Check room capacity
    if (room.maxParticipants && room.participants.size >= room.maxParticipants) {
      socket.emit('error', { message: 'Room is full' });
      return;
    }

    // Join the room
    socket.join(data.roomId);
    room.participants.add(userId);

    // Update user presence
    const userPresence = this.connectedUsers.get(userId);
    if (userPresence) {
      userPresence.currentRoom = data.roomId;
      this.connectedUsers.set(userId, userPresence);
    }

    // Send room data to user
    socket.emit('room_joined', {
      room: this.serializeRoom(room),
      messages: this.roomMessages.get(data.roomId)?.slice(-50) || [], // Last 50 messages
      participants: this.getRoomParticipants(data.roomId)
    });

    // Broadcast user joined to room
    socket.to(data.roomId).emit('user_joined_room', {
      userId,
      username: userPresence?.username,
      timestamp: new Date()
    });

    logger.info(`👥 User ${userId} joined room ${data.roomId}`);
  }

  /**
   * Handle leaving a room
   */
  private handleLeaveRoom(socket: Socket, data: { roomId: string }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    const room = this.researchRooms.get(data.roomId);
    if (room) {
      room.participants.delete(userId);
      socket.leave(data.roomId);

      // Update user presence
      const userPresence = this.connectedUsers.get(userId);
      if (userPresence && userPresence.currentRoom === data.roomId) {
        userPresence.currentRoom = undefined;
        this.connectedUsers.set(userId, userPresence);
      }

      // Broadcast user left to room
      socket.to(data.roomId).emit('user_left_room', {
        userId,
        username: userPresence?.username,
        timestamp: new Date()
      });

      logger.info(`👋 User ${userId} left room ${data.roomId}`);
    }
  }

  /**
   * Handle creating a new room
   */
  private handleCreateRoom(socket: Socket, data: Partial<ResearchRoom>): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) {
      socket.emit('error', { message: 'Not authenticated' });
      return;
    }

    const roomId = `room_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const room: ResearchRoom = {
      id: roomId,
      name: data.name || 'New Research Room',
      description: data.description,
      type: data.type || 'research',
      createdBy: userId,
      createdAt: new Date(),
      participants: new Set([userId]),
      isPrivate: data.isPrivate || false,
      maxParticipants: data.maxParticipants,
      settings: {
        allowComments: true,
        allowEditing: true,
        autoSave: true,
        notifications: true,
        ...data.settings
      }
    };

    this.researchRooms.set(roomId, room);
    this.roomMessages.set(roomId, []);

    // Join creator to the room
    socket.join(roomId);

    // Update user presence
    const userPresence = this.connectedUsers.get(userId);
    if (userPresence) {
      userPresence.currentRoom = roomId;
      this.connectedUsers.set(userId, userPresence);
    }

    socket.emit('room_created', {
      room: this.serializeRoom(room)
    });

    logger.info(`🏠 Room created: ${room.name} (${roomId}) by ${userId}`);
  }

  /**
   * Handle starting research session
   */
  private handleStartResearch(socket: Socket, data: { type: string; query: string; roomId?: string }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    const sessionId = `research_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const researchProgress: ResearchProgress = {
      sessionId,
      roomId: data.roomId,
      userId,
      type: data.type as any,
      status: 'started',
      progress: 0,
      currentStep: 'Initializing research...',
      totalSteps: 5,
      timestamp: new Date()
    };

    this.activeResearchSessions.set(sessionId, researchProgress);

    // Update user presence
    const userPresence = this.connectedUsers.get(userId);
    if (userPresence) {
      userPresence.activeResearch = sessionId;
      this.connectedUsers.set(userId, userPresence);
    }

    // Emit to user and room
    const targets = [socket];
    if (data.roomId) {
      targets.push(socket.to(data.roomId));
    }

    targets.forEach(target => {
      target.emit('research_started', {
        sessionId,
        progress: researchProgress,
        query: data.query
      });
    });

    logger.info(`🔬 Research started: ${sessionId} by ${userId}`);
  }

  /**
   * Handle research progress updates
   */
  private handleResearchProgress(socket: Socket, data: Partial<ResearchProgress>): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId || !data.sessionId) return;

    const session = this.activeResearchSessions.get(data.sessionId);
    if (!session || session.userId !== userId) return;

    // Update session
    Object.assign(session, data, { timestamp: new Date() });
    this.activeResearchSessions.set(data.sessionId, session);

    // Emit progress update
    const targets = [socket];
    if (session.roomId) {
      targets.push(socket.to(session.roomId));
    }

    targets.forEach(target => {
      target.emit('research_progress_update', {
        sessionId: data.sessionId,
        progress: session
      });
    });

    // If completed, clean up
    if (session.status === 'completed' || session.status === 'error') {
      setTimeout(() => {
        this.activeResearchSessions.delete(data.sessionId!);
        const userPresence = this.connectedUsers.get(userId);
        if (userPresence && userPresence.activeResearch === data.sessionId) {
          userPresence.activeResearch = undefined;
          this.connectedUsers.set(userId, userPresence);
        }
      }, 60000); // Clean up after 1 minute
    }
  }

  /**
   * Handle sending messages
   */
  private handleSendMessage(socket: Socket, data: { roomId: string; content: string; type?: string; replyTo?: string }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    const room = this.researchRooms.get(data.roomId);
    if (!room || !room.participants.has(userId)) {
      socket.emit('error', { message: 'Not in room or room not found' });
      return;
    }

    const userPresence = this.connectedUsers.get(userId);
    const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const message: RoomMessage = {
      id: messageId,
      roomId: data.roomId,
      userId,
      username: userPresence?.username || 'Unknown',
      type: (data.type as any) || 'text',
      content: data.content,
      replyTo: data.replyTo,
      reactions: new Map(),
      timestamp: new Date()
    };

    // Store message
    if (!this.roomMessages.has(data.roomId)) {
      this.roomMessages.set(data.roomId, []);
    }
    const messages = this.roomMessages.get(data.roomId)!;
    messages.push(message);

    // Keep only last 1000 messages per room
    if (messages.length > 1000) {
      this.roomMessages.set(data.roomId, messages.slice(-1000));
    }

    // Broadcast to room
    this.io.to(data.roomId).emit('new_message', message);

    logger.debug(`💬 Message sent in room ${data.roomId} by ${userId}`);
  }

  /**
   * Handle adding reactions to messages
   */
  private handleAddReaction(socket: Socket, data: { messageId: string; emoji: string }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    // Find message in all rooms (could be optimized with message index)
    for (const [roomId, messages] of this.roomMessages) {
      const message = messages.find(m => m.id === data.messageId);
      if (message) {
        if (!message.reactions.has(data.emoji)) {
          message.reactions.set(data.emoji, []);
        }
        
        const users = message.reactions.get(data.emoji)!;
        if (!users.includes(userId)) {
          users.push(userId);
          
          // Broadcast reaction update
          this.io.to(roomId).emit('reaction_added', {
            messageId: data.messageId,
            emoji: data.emoji,
            userId,
            totalReactions: message.reactions
          });
        }
        break;
      }
    }
  }

  /**
   * Handle user status updates
   */
  private handleUpdateStatus(socket: Socket, data: { status: UserPresence['status'] }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    const userPresence = this.connectedUsers.get(userId);
    if (userPresence) {
      userPresence.status = data.status;
      userPresence.lastSeen = new Date();
      this.connectedUsers.set(userId, userPresence);

      this.broadcastUserPresence(userId, data.status);
    }
  }

  /**
   * Handle typing indicators
   */
  private handleTypingStart(socket: Socket, data: { roomId: string }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    socket.to(data.roomId).emit('user_typing', {
      userId,
      username: this.connectedUsers.get(userId)?.username,
      isTyping: true
    });
  }

  private handleTypingStop(socket: Socket, data: { roomId: string }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    socket.to(data.roomId).emit('user_typing', {
      userId,
      username: this.connectedUsers.get(userId)?.username,
      isTyping: false
    });
  }

  /**
   * Handle marking notifications as read
   */
  private handleMarkNotificationRead(socket: Socket, data: { notificationId: string }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    const userNotifications = this.notifications.get(userId) || [];
    const notificationIndex = userNotifications.findIndex(n => n.id === data.notificationId);
    
    if (notificationIndex !== -1) {
      userNotifications.splice(notificationIndex, 1);
      this.notifications.set(userId, userNotifications);
      
      socket.emit('notification_read', { notificationId: data.notificationId });
    }
  }

  /**
   * Handle user disconnect
   */
  private handleDisconnect(socket: Socket, reason: string): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    // Remove socket from user mapping
    const userSocketSet = this.userSockets.get(userId);
    if (userSocketSet) {
      userSocketSet.delete(socket.id);
      if (userSocketSet.size === 0) {
        // User has no more connections
        this.userSockets.delete(userId);
        
        // Update user presence to offline
        const userPresence = this.connectedUsers.get(userId);
        if (userPresence) {
          userPresence.status = 'offline';
          userPresence.lastSeen = new Date();
          this.connectedUsers.set(userId, userPresence);
          
          this.broadcastUserPresence(userId, 'offline');
        }
      }
    }

    this.socketUsers.delete(socket.id);
    logger.info(`🔌 User disconnected: ${socket.id} (${userId}) - ${reason}`);
  }

  // Helper methods
  private serializeRoom(room: ResearchRoom): any {
    return {
      ...room,
      participants: Array.from(room.participants)
    };
  }

  private getUserRooms(userId: string): ResearchRoom[] {
    return Array.from(this.researchRooms.values())
      .filter(room => room.participants.has(userId))
      .map(room => this.serializeRoom(room));
  }

  private getRoomParticipants(roomId: string): UserPresence[] {
    const room = this.researchRooms.get(roomId);
    if (!room) return [];

    return Array.from(room.participants)
      .map(userId => this.connectedUsers.get(userId))
      .filter(Boolean) as UserPresence[];
  }

  private getUserNotifications(userId: string): LiveNotification[] {
    return this.notifications.get(userId) || [];
  }

  private broadcastUserPresence(userId: string, status: UserPresence['status']): void {
    const userPresence = this.connectedUsers.get(userId);
    if (!userPresence) return;

    // Broadcast to all rooms user is in
    for (const room of this.researchRooms.values()) {
      if (room.participants.has(userId)) {
        this.io.to(room.id).emit('user_presence_update', {
          userId,
          username: userPresence.username,
          status,
          lastSeen: userPresence.lastSeen
        });
      }
    }
  }

  private startCleanupTasks(): void {
    // Clean up old messages every hour
    setInterval(() => {
      const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      
      for (const [roomId, messages] of this.roomMessages) {
        const filteredMessages = messages.filter(m => m.timestamp > oneWeekAgo);
        this.roomMessages.set(roomId, filteredMessages);
      }
    }, 60 * 60 * 1000);

    // Clean up expired notifications every 10 minutes
    setInterval(() => {
      const now = new Date();
      
      for (const [userId, notifications] of this.notifications) {
        const validNotifications = notifications.filter(n => 
          !n.expiresAt || n.expiresAt > now
        );
        this.notifications.set(userId, validNotifications);
      }
    }, 10 * 60 * 1000);
  }

  /**
   * Public API methods
   */
  
  public sendNotification(notification: Omit<LiveNotification, 'id' | 'timestamp'>): void {
    const fullNotification: LiveNotification = {
      ...notification,
      id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date()
    };

    if (notification.userId) {
      // Send to specific user
      if (!this.notifications.has(notification.userId)) {
        this.notifications.set(notification.userId, []);
      }
      this.notifications.get(notification.userId)!.push(fullNotification);
      
      this.io.to(`user:${notification.userId}`).emit('new_notification', fullNotification);
    } else if (notification.roomId) {
      // Send to room
      this.io.to(notification.roomId).emit('new_notification', fullNotification);
    } else {
      // Broadcast to all
      this.io.emit('new_notification', fullNotification);
    }
  }

  public updateResearchProgress(sessionId: string, progress: Partial<ResearchProgress>): void {
    const session = this.activeResearchSessions.get(sessionId);
    if (!session) return;

    Object.assign(session, progress, { timestamp: new Date() });
    this.activeResearchSessions.set(sessionId, session);

    // Emit to user and room
    const userSockets = this.userSockets.get(session.userId);
    if (userSockets) {
      userSockets.forEach(socketId => {
        this.io.to(socketId).emit('research_progress_update', {
          sessionId,
          progress: session
        });
      });
    }

    if (session.roomId) {
      this.io.to(session.roomId).emit('research_progress_update', {
        sessionId,
        progress: session
      });
    }
  }

  public getConnectedUsers(): UserPresence[] {
    return Array.from(this.connectedUsers.values());
  }

  public getRooms(): ResearchRoom[] {
    return Array.from(this.researchRooms.values()).map(room => this.serializeRoom(room));
  }

  public getActiveResearchSessions(): ResearchProgress[] {
    return Array.from(this.activeResearchSessions.values());
  }
}

export default EnhancedWebSocketService;
