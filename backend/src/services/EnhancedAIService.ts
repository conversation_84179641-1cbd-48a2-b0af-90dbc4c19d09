import { EventEmitter } from 'events';
import { logger } from '../utils/logger';
import { performanceMonitoringService } from './PerformanceMonitoringService';
import { enhancedCacheService } from './EnhancedCacheService';

export interface AIModel {
  id: string;
  name: string;
  provider: 'openai' | 'anthropic' | 'google' | 'local' | 'huggingface';
  type: 'chat' | 'completion' | 'embedding' | 'multimodal';
  capabilities: string[];
  maxTokens: number;
  costPerToken: number;
  responseTime: number; // average in ms
  accuracy: number; // 0-100
  reliability: number; // 0-100
  specialties: string[]; // domains where this model excels
  isAvailable: boolean;
  endpoint?: string;
  apiKey?: string;
  lastUsed: Date;
  usageCount: number;
  errorCount: number;
}

export interface AIRequest {
  id: string;
  prompt: string;
  context?: string;
  taskType: 'research' | 'analysis' | 'synthesis' | 'validation' | 'creative' | 'technical';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  requiresFactualAccuracy?: boolean;
  requiresCreativity?: boolean;
  requiresSpeed?: boolean;
  domain?: string;
  userId?: string;
  sessionId?: string;
  metadata?: Record<string, any>;
}

export interface AIResponse {
  id: string;
  requestId: string;
  modelId: string;
  content: string;
  confidence: number;
  quality: number;
  factualAccuracy?: number;
  creativity?: number;
  relevance?: number;
  completeness?: number;
  tokensUsed: number;
  responseTime: number;
  cost: number;
  metadata: Record<string, any>;
  timestamp: Date;
}

export interface EnsembleResult {
  finalResponse: string;
  confidence: number;
  quality: number;
  modelResponses: AIResponse[];
  consensusScore: number;
  conflictResolution?: string;
  reasoning: string;
  metadata: Record<string, any>;
}

/**
 * Enhanced AI Service with multi-model ensemble capabilities
 */
export class EnhancedAIService extends EventEmitter {
  private models: Map<string, AIModel> = new Map();
  private activeRequests: Map<string, AIRequest> = new Map();
  private responseHistory: AIResponse[] = [];
  private ensembleStrategies: Map<string, Function> = new Map();
  private modelPerformanceCache: Map<string, any> = new Map();

  constructor() {
    super();
    this.initializeModels();
    this.initializeEnsembleStrategies();
    this.startPerformanceMonitoring();
    logger.info('🧠 Enhanced AI Service initialized with multi-model support');
  }

  /**
   * Initialize available AI models
   */
  private initializeModels(): void {
    const models: Omit<AIModel, 'lastUsed' | 'usageCount' | 'errorCount'>[] = [
      {
        id: 'gpt-4-turbo',
        name: 'GPT-4 Turbo',
        provider: 'openai',
        type: 'chat',
        capabilities: ['reasoning', 'analysis', 'creative_writing', 'code_generation'],
        maxTokens: 128000,
        costPerToken: 0.00003,
        responseTime: 2000,
        accuracy: 95,
        reliability: 98,
        specialties: ['complex_reasoning', 'technical_analysis', 'research_synthesis'],
        isAvailable: true,
        endpoint: 'https://api.openai.com/v1/chat/completions'
      },
      {
        id: 'claude-3-sonnet',
        name: 'Claude 3 Sonnet',
        provider: 'anthropic',
        type: 'chat',
        capabilities: ['reasoning', 'analysis', 'safety', 'long_context'],
        maxTokens: 200000,
        costPerToken: 0.000015,
        responseTime: 1800,
        accuracy: 94,
        reliability: 97,
        specialties: ['safety_analysis', 'ethical_reasoning', 'long_document_analysis'],
        isAvailable: true,
        endpoint: 'https://api.anthropic.com/v1/messages'
      },
      {
        id: 'gemini-pro',
        name: 'Gemini Pro',
        provider: 'google',
        type: 'multimodal',
        capabilities: ['reasoning', 'multimodal', 'code_generation', 'math'],
        maxTokens: 32000,
        costPerToken: 0.0000005,
        responseTime: 1500,
        accuracy: 92,
        reliability: 95,
        specialties: ['mathematical_reasoning', 'multimodal_analysis', 'fast_inference'],
        isAvailable: true,
        endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent'
      },
      {
        id: 'local-gemma3-4b',
        name: 'Local Gemma3 4B',
        provider: 'local',
        type: 'chat',
        capabilities: ['reasoning', 'analysis', 'privacy'],
        maxTokens: 8192,
        costPerToken: 0,
        responseTime: 800,
        accuracy: 88,
        reliability: 92,
        specialties: ['privacy_focused', 'fast_local_inference', 'cost_effective'],
        isAvailable: true,
        endpoint: 'http://192.168.0.179:1234/v1/chat/completions'
      },
      {
        id: 'bielik-v3',
        name: 'Bielik V3',
        provider: 'local',
        type: 'chat',
        capabilities: ['polish_language', 'reasoning', 'cultural_context'],
        maxTokens: 4096,
        costPerToken: 0,
        responseTime: 1200,
        accuracy: 85,
        reliability: 90,
        specialties: ['polish_language', 'cultural_context', 'local_knowledge'],
        isAvailable: false, // Will be enabled when available
        endpoint: 'http://localhost:8080/v1/chat/completions'
      }
    ];

    models.forEach(model => {
      this.models.set(model.id, {
        ...model,
        lastUsed: new Date(),
        usageCount: 0,
        errorCount: 0
      });
    });
  }

  /**
   * Initialize ensemble strategies
   */
  private initializeEnsembleStrategies(): void {
    // Majority voting strategy
    this.ensembleStrategies.set('majority_voting', (responses: AIResponse[]) => {
      const votes = new Map<string, number>();
      responses.forEach(response => {
        const key = response.content.substring(0, 100); // Use first 100 chars as key
        votes.set(key, (votes.get(key) || 0) + 1);
      });
      
      const winner = Array.from(votes.entries()).reduce((a, b) => a[1] > b[1] ? a : b);
      return responses.find(r => r.content.startsWith(winner[0]));
    });

    // Confidence-weighted strategy
    this.ensembleStrategies.set('confidence_weighted', (responses: AIResponse[]) => {
      return responses.reduce((best, current) => 
        current.confidence > best.confidence ? current : best
      );
    });

    // Quality-weighted strategy
    this.ensembleStrategies.set('quality_weighted', (responses: AIResponse[]) => {
      return responses.reduce((best, current) => 
        current.quality > best.quality ? current : best
      );
    });

    // Hybrid strategy (confidence + quality + model reliability)
    this.ensembleStrategies.set('hybrid', (responses: AIResponse[]) => {
      return responses.reduce((best, current) => {
        const currentModel = this.models.get(current.modelId);
        const bestModel = this.models.get(best.modelId);
        
        const currentScore = (current.confidence * 0.4) + (current.quality * 0.4) + 
                           ((currentModel?.reliability || 0) * 0.2);
        const bestScore = (best.confidence * 0.4) + (best.quality * 0.4) + 
                         ((bestModel?.reliability || 0) * 0.2);
        
        return currentScore > bestScore ? current : best;
      });
    });
  }

  /**
   * Process AI request with intelligent model selection
   */
  public async processRequest(request: AIRequest): Promise<EnsembleResult> {
    const metricId = performanceMonitoringService.trackAIOperation('process_request', 'ensemble');
    
    try {
      // Store request
      this.activeRequests.set(request.id, request);

      // Select optimal models for this request
      const selectedModels = await this.selectOptimalModels(request);
      
      // Execute requests in parallel
      const responses = await Promise.allSettled(
        selectedModels.map(model => this.executeModelRequest(request, model))
      );

      // Filter successful responses
      const successfulResponses = responses
        .filter((result): result is PromiseFulfilledResult<AIResponse> => result.status === 'fulfilled')
        .map(result => result.value);

      if (successfulResponses.length === 0) {
        throw new Error('All model requests failed');
      }

      // Apply ensemble strategy
      const ensembleResult = await this.applyEnsembleStrategy(request, successfulResponses);

      // Cache result
      await this.cacheResult(request, ensembleResult);

      // Update model performance
      this.updateModelPerformance(successfulResponses);

      // Clean up
      this.activeRequests.delete(request.id);

      performanceMonitoringService.endMetric(metricId, true);
      
      this.emit('request_completed', {
        requestId: request.id,
        result: ensembleResult,
        modelsUsed: selectedModels.map(m => m.id)
      });

      return ensembleResult;
    } catch (error) {
      performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Request failed');
      this.activeRequests.delete(request.id);
      throw error;
    }
  }

  /**
   * Select optimal models for a request
   */
  private async selectOptimalModels(request: AIRequest): Promise<AIModel[]> {
    const availableModels = Array.from(this.models.values()).filter(m => m.isAvailable);
    
    // Score models based on request requirements
    const scoredModels = availableModels.map(model => {
      let score = 0;
      
      // Base score from accuracy and reliability
      score += (model.accuracy * 0.3) + (model.reliability * 0.2);
      
      // Task type matching
      if (model.specialties.includes(request.taskType)) {
        score += 20;
      }
      
      // Domain expertise
      if (request.domain && model.specialties.includes(request.domain)) {
        score += 15;
      }
      
      // Speed requirements
      if (request.requiresSpeed && model.responseTime < 1000) {
        score += 10;
      }
      
      // Factual accuracy requirements
      if (request.requiresFactualAccuracy && model.accuracy > 90) {
        score += 10;
      }
      
      // Creativity requirements
      if (request.requiresCreativity && model.capabilities.includes('creative_writing')) {
        score += 10;
      }
      
      // Cost consideration (higher score for lower cost)
      score += (1 - model.costPerToken / 0.00005) * 5;
      
      // Recent performance
      const recentPerformance = this.modelPerformanceCache.get(model.id);
      if (recentPerformance) {
        score += recentPerformance.averageQuality * 0.1;
      }
      
      return { model, score };
    });
    
    // Sort by score and select top models
    scoredModels.sort((a, b) => b.score - a.score);
    
    // Select 2-3 models based on priority
    const modelCount = request.priority === 'urgent' ? 3 : 
                      request.priority === 'high' ? 2 : 1;
    
    return scoredModels.slice(0, modelCount).map(item => item.model);
  }

  /**
   * Execute request on a specific model
   */
  private async executeModelRequest(request: AIRequest, model: AIModel): Promise<AIResponse> {
    const startTime = Date.now();
    const responseId = `response_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      // Check cache first
      const cacheKey = `ai_response:${model.id}:${this.hashRequest(request)}`;
      const cachedResponse = await enhancedCacheService.get<AIResponse>(cacheKey);
      
      if (cachedResponse) {
        logger.debug(`Cache hit for model ${model.id}`);
        return cachedResponse;
      }

      // Prepare request based on model provider
      const modelResponse = await this.callModel(model, request);
      
      // Calculate metrics
      const responseTime = Date.now() - startTime;
      const tokensUsed = this.estimateTokens(request.prompt + modelResponse);
      const cost = tokensUsed * model.costPerToken;
      
      // Assess response quality
      const quality = await this.assessResponseQuality(request, modelResponse);
      const confidence = await this.assessConfidence(modelResponse);
      
      const response: AIResponse = {
        id: responseId,
        requestId: request.id,
        modelId: model.id,
        content: modelResponse,
        confidence,
        quality,
        tokensUsed,
        responseTime,
        cost,
        metadata: {
          provider: model.provider,
          modelName: model.name,
          temperature: request.temperature || 0.7,
          maxTokens: request.maxTokens || 2000
        },
        timestamp: new Date()
      };

      // Update model stats
      model.lastUsed = new Date();
      model.usageCount++;
      model.responseTime = (model.responseTime + responseTime) / 2; // Moving average

      // Cache response
      await enhancedCacheService.set(cacheKey, response, { 
        ttl: 3600, 
        tags: ['ai_responses', model.id, request.taskType] 
      });

      // Store in history
      this.responseHistory.push(response);
      if (this.responseHistory.length > 10000) {
        this.responseHistory = this.responseHistory.slice(-10000);
      }

      return response;
    } catch (error) {
      model.errorCount++;
      logger.error(`Model ${model.id} request failed:`, error);
      throw error;
    }
  }

  /**
   * Call specific model API
   */
  private async callModel(model: AIModel, request: AIRequest): Promise<string> {
    const metricId = performanceMonitoringService.trackExternalAPI(model.provider, model.endpoint || 'unknown');
    
    try {
      let response: string;

      switch (model.provider) {
        case 'openai':
          response = await this.callOpenAI(model, request);
          break;
        case 'anthropic':
          response = await this.callAnthropic(model, request);
          break;
        case 'google':
          response = await this.callGoogle(model, request);
          break;
        case 'local':
          response = await this.callLocalModel(model, request);
          break;
        default:
          throw new Error(`Unsupported provider: ${model.provider}`);
      }

      performanceMonitoringService.endMetric(metricId, true);
      return response;
    } catch (error) {
      performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Model call failed');
      throw error;
    }
  }

  /**
   * Call OpenAI API
   */
  private async callOpenAI(model: AIModel, request: AIRequest): Promise<string> {
    const response = await fetch(model.endpoint!, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${model.apiKey || process.env.OPENAI_API_KEY}`
      },
      body: JSON.stringify({
        model: model.id,
        messages: [
          { role: 'system', content: 'You are a helpful research assistant.' },
          { role: 'user', content: request.prompt }
        ],
        max_tokens: request.maxTokens || 2000,
        temperature: request.temperature || 0.7,
        top_p: request.topP || 1.0
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  /**
   * Call Anthropic API
   */
  private async callAnthropic(model: AIModel, request: AIRequest): Promise<string> {
    const response = await fetch(model.endpoint!, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': model.apiKey || process.env.ANTHROPIC_API_KEY || '',
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: model.id,
        max_tokens: request.maxTokens || 2000,
        messages: [
          { role: 'user', content: request.prompt }
        ],
        temperature: request.temperature || 0.7
      })
    });

    if (!response.ok) {
      throw new Error(`Anthropic API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.content[0].text;
  }

  /**
   * Call Google API
   */
  private async callGoogle(model: AIModel, request: AIRequest): Promise<string> {
    const response = await fetch(`${model.endpoint}?key=${model.apiKey || process.env.GOOGLE_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [{
          parts: [{ text: request.prompt }]
        }],
        generationConfig: {
          temperature: request.temperature || 0.7,
          maxOutputTokens: request.maxTokens || 2000,
          topP: request.topP || 1.0
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Google API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.candidates[0].content.parts[0].text;
  }

  /**
   * Call local model (LM Studio)
   */
  private async callLocalModel(model: AIModel, request: AIRequest): Promise<string> {
    const response = await fetch(model.endpoint!, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: model.name,
        messages: [
          { role: 'system', content: 'You are a helpful research assistant.' },
          { role: 'user', content: request.prompt }
        ],
        max_tokens: request.maxTokens || 2000,
        temperature: request.temperature || 0.7,
        stream: false
      })
    });

    if (!response.ok) {
      throw new Error(`Local model API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  /**
   * Apply ensemble strategy to combine responses
   */
  private async applyEnsembleStrategy(request: AIRequest, responses: AIResponse[]): Promise<EnsembleResult> {
    if (responses.length === 1) {
      return {
        finalResponse: responses[0].content,
        confidence: responses[0].confidence,
        quality: responses[0].quality,
        modelResponses: responses,
        consensusScore: 100,
        reasoning: 'Single model response',
        metadata: { strategy: 'single_model' }
      };
    }

    // Select strategy based on request type and requirements
    let strategyName = 'hybrid';
    if (request.requiresFactualAccuracy) {
      strategyName = 'quality_weighted';
    } else if (request.requiresSpeed) {
      strategyName = 'confidence_weighted';
    }

    const strategy = this.ensembleStrategies.get(strategyName)!;
    const selectedResponse = strategy(responses);

    // Calculate consensus score
    const consensusScore = this.calculateConsensusScore(responses);

    // Generate reasoning
    const reasoning = this.generateEnsembleReasoning(responses, selectedResponse, strategyName);

    return {
      finalResponse: selectedResponse.content,
      confidence: selectedResponse.confidence,
      quality: selectedResponse.quality,
      modelResponses: responses,
      consensusScore,
      reasoning,
      metadata: {
        strategy: strategyName,
        modelsUsed: responses.map(r => r.modelId),
        totalCost: responses.reduce((sum, r) => sum + r.cost, 0),
        averageResponseTime: responses.reduce((sum, r) => sum + r.responseTime, 0) / responses.length
      }
    };
  }

  // Helper methods
  private hashRequest(request: AIRequest): string {
    const key = `${request.prompt}_${request.taskType}_${request.maxTokens || 2000}_${request.temperature || 0.7}`;
    return Buffer.from(key).toString('base64').substring(0, 32);
  }

  private estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }

  private async assessResponseQuality(request: AIRequest, response: string): Promise<number> {
    // Simplified quality assessment
    let quality = 50; // Base quality
    
    // Length appropriateness
    if (response.length > 100 && response.length < 5000) quality += 20;
    
    // Coherence (simple check for complete sentences)
    if (response.includes('.') && response.includes(' ')) quality += 15;
    
    // Relevance (keyword matching)
    const requestWords = request.prompt.toLowerCase().split(' ');
    const responseWords = response.toLowerCase().split(' ');
    const overlap = requestWords.filter(word => responseWords.includes(word)).length;
    quality += Math.min(overlap * 2, 15);
    
    return Math.min(quality, 100);
  }

  private async assessConfidence(response: string): Promise<number> {
    // Simplified confidence assessment
    let confidence = 70; // Base confidence
    
    // Check for uncertainty markers
    const uncertaintyMarkers = ['maybe', 'perhaps', 'might', 'could be', 'uncertain'];
    const hasUncertainty = uncertaintyMarkers.some(marker => 
      response.toLowerCase().includes(marker)
    );
    
    if (hasUncertainty) confidence -= 20;
    
    // Check for confident language
    const confidentMarkers = ['definitely', 'certainly', 'clearly', 'obviously'];
    const hasConfidence = confidentMarkers.some(marker => 
      response.toLowerCase().includes(marker)
    );
    
    if (hasConfidence) confidence += 15;
    
    return Math.min(Math.max(confidence, 0), 100);
  }

  private calculateConsensusScore(responses: AIResponse[]): number {
    if (responses.length < 2) return 100;
    
    // Simple similarity check based on response length and first words
    const similarities: number[] = [];
    
    for (let i = 0; i < responses.length; i++) {
      for (let j = i + 1; j < responses.length; j++) {
        const resp1 = responses[i].content.toLowerCase();
        const resp2 = responses[j].content.toLowerCase();
        
        // Length similarity
        const lengthSim = 1 - Math.abs(resp1.length - resp2.length) / Math.max(resp1.length, resp2.length);
        
        // First 100 characters similarity
        const prefix1 = resp1.substring(0, 100);
        const prefix2 = resp2.substring(0, 100);
        const prefixSim = prefix1 === prefix2 ? 1 : 0.5;
        
        similarities.push((lengthSim + prefixSim) / 2);
      }
    }
    
    const avgSimilarity = similarities.reduce((sum, sim) => sum + sim, 0) / similarities.length;
    return Math.round(avgSimilarity * 100);
  }

  private generateEnsembleReasoning(responses: AIResponse[], selected: AIResponse, strategy: string): string {
    const modelNames = responses.map(r => this.models.get(r.modelId)?.name || r.modelId);
    const selectedModel = this.models.get(selected.modelId)?.name || selected.modelId;
    
    return `Selected response from ${selectedModel} using ${strategy} strategy. ` +
           `Evaluated ${responses.length} models: ${modelNames.join(', ')}. ` +
           `Selection based on confidence (${selected.confidence}%), quality (${selected.quality}%), ` +
           `and model reliability.`;
  }

  private async cacheResult(request: AIRequest, result: EnsembleResult): Promise<void> {
    const cacheKey = `ensemble_result:${this.hashRequest(request)}`;
    await enhancedCacheService.set(cacheKey, result, {
      ttl: 7200, // 2 hours
      tags: ['ensemble_results', request.taskType]
    });
  }

  private updateModelPerformance(responses: AIResponse[]): void {
    responses.forEach(response => {
      const modelId = response.modelId;
      const current = this.modelPerformanceCache.get(modelId) || {
        averageQuality: 0,
        averageConfidence: 0,
        averageResponseTime: 0,
        requestCount: 0
      };
      
      current.requestCount++;
      current.averageQuality = (current.averageQuality * (current.requestCount - 1) + response.quality) / current.requestCount;
      current.averageConfidence = (current.averageConfidence * (current.requestCount - 1) + response.confidence) / current.requestCount;
      current.averageResponseTime = (current.averageResponseTime * (current.requestCount - 1) + response.responseTime) / current.requestCount;
      
      this.modelPerformanceCache.set(modelId, current);
    });
  }

  private startPerformanceMonitoring(): void {
    // Monitor model availability every 5 minutes
    setInterval(async () => {
      for (const [modelId, model] of this.models) {
        try {
          // Simple health check
          if (model.provider === 'local' && model.endpoint) {
            const response = await fetch(model.endpoint.replace('/chat/completions', '/models'), {
              method: 'GET',
              timeout: 5000
            } as any);
            model.isAvailable = response.ok;
          }
        } catch (error) {
          model.isAvailable = false;
          logger.warn(`Model ${modelId} health check failed:`, error);
        }
      }
    }, 300000);
  }

  // Public API methods
  public getAvailableModels(): AIModel[] {
    return Array.from(this.models.values()).filter(m => m.isAvailable);
  }

  public getModelPerformance(): Record<string, any> {
    const performance: Record<string, any> = {};
    
    for (const [modelId, model] of this.models) {
      const cached = this.modelPerformanceCache.get(modelId);
      performance[modelId] = {
        model: {
          name: model.name,
          provider: model.provider,
          accuracy: model.accuracy,
          reliability: model.reliability,
          isAvailable: model.isAvailable
        },
        usage: {
          usageCount: model.usageCount,
          errorCount: model.errorCount,
          errorRate: model.usageCount > 0 ? (model.errorCount / model.usageCount) * 100 : 0,
          lastUsed: model.lastUsed
        },
        performance: cached || {
          averageQuality: 0,
          averageConfidence: 0,
          averageResponseTime: model.responseTime,
          requestCount: 0
        }
      };
    }
    
    return performance;
  }

  public async generateResponse(prompt: string, options: Partial<AIRequest> = {}): Promise<EnsembleResult> {
    const request: AIRequest = {
      id: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      prompt,
      taskType: 'research',
      priority: 'medium',
      ...options
    };

    return this.processRequest(request);
  }

  public getResponseHistory(limit: number = 100): AIResponse[] {
    return this.responseHistory.slice(-limit);
  }
}

export default EnhancedAIService;
