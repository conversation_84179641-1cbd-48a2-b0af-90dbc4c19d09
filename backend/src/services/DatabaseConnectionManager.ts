import { Driver as Neo4jDriver, Session as Neo4jSession } from 'neo4j-driver';
import { MongoClient, Db as MongoDb } from 'mongodb';
import { createClient, RedisClientType } from 'redis';
import weaviate, { WeaviateClient } from 'weaviate-ts-client';
import { logger } from '../utils/logger';
import { config } from '../config/environment';
import { performanceMonitoringService } from './PerformanceMonitoringService';

export interface ConnectionPoolStats {
  neo4j: {
    active: number;
    idle: number;
    total: number;
    maxSize: number;
  };
  mongodb: {
    active: number;
    idle: number;
    total: number;
    maxSize: number;
  };
  redis: {
    connected: boolean;
    ready: boolean;
    reconnecting: boolean;
  };
  weaviate: {
    connected: boolean;
    lastPing: Date | null;
  };
}

export interface DatabaseHealth {
  neo4j: {
    status: 'healthy' | 'warning' | 'critical';
    responseTime: number;
    errorRate: number;
    lastCheck: Date;
  };
  mongodb: {
    status: 'healthy' | 'warning' | 'critical';
    responseTime: number;
    errorRate: number;
    lastCheck: Date;
  };
  redis: {
    status: 'healthy' | 'warning' | 'critical';
    responseTime: number;
    errorRate: number;
    lastCheck: Date;
  };
  weaviate: {
    status: 'healthy' | 'warning' | 'critical';
    responseTime: number;
    errorRate: number;
    lastCheck: Date;
  };
}

/**
 * Advanced database connection manager with pooling and health monitoring
 */
export class DatabaseConnectionManager {
  private neo4jDriver: Neo4jDriver | null = null;
  private mongoClient: MongoClient | null = null;
  private redisClient: RedisClientType | null = null;
  private weaviateClient: WeaviateClient | null = null;
  
  private neo4jSessions: Set<Neo4jSession> = new Set();
  private connectionHealth: DatabaseHealth;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  
  constructor() {
    this.connectionHealth = this.initializeHealthStatus();
    this.startHealthMonitoring();
  }

  /**
   * Initialize all database connections with pooling
   */
  async initializeConnections(): Promise<void> {
    logger.info('Initializing database connections with advanced pooling...');

    try {
      await Promise.allSettled([
        this.initializeNeo4j(),
        this.initializeMongoDB(),
        this.initializeRedis(),
        this.initializeWeaviate()
      ]);

      logger.info('✅ Database connection manager initialized');
    } catch (error) {
      logger.error('❌ Failed to initialize database connections:', error);
      throw error;
    }
  }

  /**
   * Initialize Neo4j with connection pooling
   */
  private async initializeNeo4j(): Promise<void> {
    const metricId = performanceMonitoringService.trackDatabaseQuery('connect', 'neo4j');
    
    try {
      const neo4j = await import('neo4j-driver');
      
      this.neo4jDriver = neo4j.driver(
        config.neo4j.uri,
        neo4j.auth.basic(config.neo4j.username, config.neo4j.password),
        {
          maxConnectionLifetime: 3 * 60 * 60 * 1000, // 3 hours
          maxConnectionPoolSize: 50,
          connectionAcquisitionTimeout: 60000, // 60 seconds
          disableLosslessIntegers: true,
          connectionTimeout: 20000,
          maxTransactionRetryTime: 15000,
          logging: {
            level: config.isDevelopment ? 'debug' : 'info',
            logger: (level, message) => logger.debug(`Neo4j ${level}: ${message}`)
          }
        }
      );

      // Verify connectivity
      await this.neo4jDriver.verifyConnectivity();
      
      performanceMonitoringService.endMetric(metricId, true);
      logger.info('✅ Neo4j connection pool initialized');
    } catch (error) {
      performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Neo4j connection failed');
      logger.error('❌ Neo4j connection failed:', error);
      throw error;
    }
  }

  /**
   * Initialize MongoDB with connection pooling
   */
  private async initializeMongoDB(): Promise<void> {
    const metricId = performanceMonitoringService.trackDatabaseQuery('connect', 'mongodb');
    
    try {
      this.mongoClient = new MongoClient(config.mongodb.uri, {
        maxPoolSize: 50,
        minPoolSize: 5,
        maxIdleTimeMS: 30000,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        connectTimeoutMS: 10000,
        heartbeatFrequencyMS: 10000,
        retryWrites: true,
        retryReads: true,
        compressors: ['zlib'],
        zlibCompressionLevel: 6
      });

      await this.mongoClient.connect();
      
      // Test the connection
      await this.mongoClient.db('admin').command({ ping: 1 });
      
      performanceMonitoringService.endMetric(metricId, true);
      logger.info('✅ MongoDB connection pool initialized');
    } catch (error) {
      performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'MongoDB connection failed');
      logger.error('❌ MongoDB connection failed:', error);
      throw error;
    }
  }

  /**
   * Initialize Redis with advanced configuration
   */
  private async initializeRedis(): Promise<void> {
    const metricId = performanceMonitoringService.trackDatabaseQuery('connect', 'redis');
    
    try {
      this.redisClient = createClient({
        url: config.redis.url,
        socket: {
          connectTimeout: 10000,
          lazyConnect: false,
          keepAlive: 30000,
          reconnectStrategy: (retries) => {
            const delay = Math.min(retries * 50, 500);
            logger.info(`Redis reconnecting in ${delay}ms (attempt ${retries})`);
            return delay;
          }
        },
        database: config.redis.database || 0,
        commandsQueueMaxLength: 1000,
        pingInterval: 30000
      });

      this.redisClient.on('error', (err) => {
        logger.error('Redis error:', err);
        this.updateConnectionHealth('redis', 'critical', 0, Date.now());
      });

      this.redisClient.on('connect', () => {
        logger.info('Redis connected');
        this.updateConnectionHealth('redis', 'healthy', 0, Date.now());
      });

      this.redisClient.on('ready', () => {
        logger.info('Redis ready');
      });

      this.redisClient.on('reconnecting', () => {
        logger.warn('Redis reconnecting...');
        this.updateConnectionHealth('redis', 'warning', 0, Date.now());
      });

      await this.redisClient.connect();
      
      performanceMonitoringService.endMetric(metricId, true);
      logger.info('✅ Redis connection initialized');
    } catch (error) {
      performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Redis connection failed');
      logger.error('❌ Redis connection failed:', error);
      throw error;
    }
  }

  /**
   * Initialize Weaviate with health monitoring
   */
  private async initializeWeaviate(): Promise<void> {
    const metricId = performanceMonitoringService.trackDatabaseQuery('connect', 'weaviate');
    
    try {
      this.weaviateClient = weaviate.client({
        scheme: config.weaviate.scheme,
        host: config.weaviate.host,
        headers: config.weaviate.apiKey ? {
          'X-OpenAI-Api-Key': config.weaviate.apiKey
        } : undefined
      });

      // Test connection
      await this.weaviateClient.misc.liveChecker().do();
      
      performanceMonitoringService.endMetric(metricId, true);
      logger.info('✅ Weaviate connection initialized');
    } catch (error) {
      performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Weaviate connection failed');
      logger.warn('⚠️ Weaviate connection failed, continuing without vector database');
    }
  }

  /**
   * Get Neo4j session with automatic cleanup
   */
  async getNeo4jSession(): Promise<Neo4jSession> {
    if (!this.neo4jDriver) {
      throw new Error('Neo4j driver not initialized');
    }

    const session = this.neo4jDriver.session({
      defaultAccessMode: 'WRITE',
      bookmarks: [],
      database: config.neo4j.database
    });

    this.neo4jSessions.add(session);
    
    // Auto-cleanup after 30 minutes
    setTimeout(() => {
      this.closeNeo4jSession(session);
    }, 30 * 60 * 1000);

    return session;
  }

  /**
   * Close Neo4j session
   */
  async closeNeo4jSession(session: Neo4jSession): Promise<void> {
    try {
      await session.close();
      this.neo4jSessions.delete(session);
    } catch (error) {
      logger.warn('Failed to close Neo4j session:', error);
    }
  }

  /**
   * Get MongoDB database instance
   */
  getMongoDatabase(dbName: string = config.mongodb.database): MongoDb {
    if (!this.mongoClient) {
      throw new Error('MongoDB client not initialized');
    }
    return this.mongoClient.db(dbName);
  }

  /**
   * Get Redis client
   */
  getRedisClient(): RedisClientType {
    if (!this.redisClient) {
      throw new Error('Redis client not initialized');
    }
    return this.redisClient;
  }

  /**
   * Get Weaviate client
   */
  getWeaviateClient(): WeaviateClient {
    if (!this.weaviateClient) {
      throw new Error('Weaviate client not initialized');
    }
    return this.weaviateClient;
  }

  /**
   * Get connection pool statistics
   */
  async getConnectionPoolStats(): Promise<ConnectionPoolStats> {
    const stats: ConnectionPoolStats = {
      neo4j: {
        active: this.neo4jSessions.size,
        idle: 0,
        total: this.neo4jSessions.size,
        maxSize: 50
      },
      mongodb: {
        active: 0,
        idle: 0,
        total: 0,
        maxSize: 50
      },
      redis: {
        connected: this.redisClient?.isOpen || false,
        ready: this.redisClient?.isReady || false,
        reconnecting: false
      },
      weaviate: {
        connected: this.weaviateClient !== null,
        lastPing: this.connectionHealth.weaviate.lastCheck
      }
    };

    // Get MongoDB connection stats if available
    if (this.mongoClient) {
      try {
        const adminDb = this.mongoClient.db('admin');
        const serverStatus = await adminDb.command({ serverStatus: 1 });
        stats.mongodb.active = serverStatus.connections?.current || 0;
        stats.mongodb.total = serverStatus.connections?.current || 0;
      } catch (error) {
        logger.warn('Failed to get MongoDB connection stats:', error);
      }
    }

    return stats;
  }

  /**
   * Get database health status
   */
  getDatabaseHealth(): DatabaseHealth {
    return { ...this.connectionHealth };
  }

  /**
   * Perform health checks on all databases
   */
  async performHealthChecks(): Promise<void> {
    const checks = [
      this.checkNeo4jHealth(),
      this.checkMongoDBHealth(),
      this.checkRedisHealth(),
      this.checkWeaviateHealth()
    ];

    await Promise.allSettled(checks);
  }

  /**
   * Graceful shutdown of all connections
   */
  async shutdown(): Promise<void> {
    logger.info('Shutting down database connections...');

    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    const shutdownPromises = [];

    // Close Neo4j sessions and driver
    if (this.neo4jDriver) {
      for (const session of this.neo4jSessions) {
        shutdownPromises.push(this.closeNeo4jSession(session));
      }
      shutdownPromises.push(this.neo4jDriver.close());
    }

    // Close MongoDB connection
    if (this.mongoClient) {
      shutdownPromises.push(this.mongoClient.close());
    }

    // Close Redis connection
    if (this.redisClient) {
      shutdownPromises.push(this.redisClient.quit());
    }

    await Promise.allSettled(shutdownPromises);
    logger.info('✅ All database connections closed');
  }

  // Private helper methods
  private initializeHealthStatus(): DatabaseHealth {
    const now = new Date();
    return {
      neo4j: { status: 'critical', responseTime: 0, errorRate: 0, lastCheck: now },
      mongodb: { status: 'critical', responseTime: 0, errorRate: 0, lastCheck: now },
      redis: { status: 'critical', responseTime: 0, errorRate: 0, lastCheck: now },
      weaviate: { status: 'critical', responseTime: 0, errorRate: 0, lastCheck: now }
    };
  }

  private startHealthMonitoring(): void {
    // Perform health checks every 30 seconds
    this.healthCheckInterval = setInterval(async () => {
      try {
        await this.performHealthChecks();
      } catch (error) {
        logger.error('Health check failed:', error);
      }
    }, 30000);
  }

  private async checkNeo4jHealth(): Promise<void> {
    const startTime = Date.now();
    try {
      if (this.neo4jDriver) {
        await this.neo4jDriver.verifyConnectivity();
        const responseTime = Date.now() - startTime;
        this.updateConnectionHealth('neo4j', 'healthy', responseTime, startTime);
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.updateConnectionHealth('neo4j', 'critical', responseTime, startTime);
      logger.warn('Neo4j health check failed:', error);
    }
  }

  private async checkMongoDBHealth(): Promise<void> {
    const startTime = Date.now();
    try {
      if (this.mongoClient) {
        await this.mongoClient.db('admin').command({ ping: 1 });
        const responseTime = Date.now() - startTime;
        this.updateConnectionHealth('mongodb', 'healthy', responseTime, startTime);
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.updateConnectionHealth('mongodb', 'critical', responseTime, startTime);
      logger.warn('MongoDB health check failed:', error);
    }
  }

  private async checkRedisHealth(): Promise<void> {
    const startTime = Date.now();
    try {
      if (this.redisClient?.isReady) {
        await this.redisClient.ping();
        const responseTime = Date.now() - startTime;
        this.updateConnectionHealth('redis', 'healthy', responseTime, startTime);
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.updateConnectionHealth('redis', 'critical', responseTime, startTime);
      logger.warn('Redis health check failed:', error);
    }
  }

  private async checkWeaviateHealth(): Promise<void> {
    const startTime = Date.now();
    try {
      if (this.weaviateClient) {
        await this.weaviateClient.misc.liveChecker().do();
        const responseTime = Date.now() - startTime;
        this.updateConnectionHealth('weaviate', 'healthy', responseTime, startTime);
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.updateConnectionHealth('weaviate', 'critical', responseTime, startTime);
      logger.warn('Weaviate health check failed:', error);
    }
  }

  private updateConnectionHealth(
    database: keyof DatabaseHealth,
    status: 'healthy' | 'warning' | 'critical',
    responseTime: number,
    timestamp: number
  ): void {
    this.connectionHealth[database] = {
      status,
      responseTime,
      errorRate: status === 'critical' ? 100 : 0,
      lastCheck: new Date(timestamp)
    };
  }
}

export const databaseConnectionManager = new DatabaseConnectionManager();
