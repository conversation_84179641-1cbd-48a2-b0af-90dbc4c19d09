"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CognitiveResearchAssistant = void 0;
const events_1 = require("events");
const logger_1 = require("../utils/logger");
const PerformanceMonitoringService_1 = require("./PerformanceMonitoringService");
const EnhancedCacheService_1 = require("./EnhancedCacheService");
class CognitiveResearchAssistant extends events_1.EventEmitter {
    aiService;
    promptEngine;
    strategyManager;
    userContexts = new Map();
    intentClassifier = new Map();
    learningEngine = new Map();
    constructor(aiService, promptEngine, strategyManager) {
        super();
        this.aiService = aiService;
        this.promptEngine = promptEngine;
        this.strategyManager = strategyManager;
        this.initializeIntentClassifier();
        this.startLearningLoop();
        logger_1.logger.info('🧠 Cognitive Research Assistant initialized with learning capabilities');
    }
    initializeIntentClassifier() {
        const intentPatterns = {
            research: [
                'research', 'find information', 'look up', 'investigate', 'study', 'explore',
                'what is', 'tell me about', 'information on', 'details about'
            ],
            analysis: [
                'analyze', 'examine', 'evaluate', 'assess', 'compare', 'contrast',
                'pros and cons', 'advantages', 'disadvantages', 'strengths', 'weaknesses'
            ],
            synthesis: [
                'summarize', 'combine', 'integrate', 'synthesize', 'merge', 'consolidate',
                'bring together', 'overall picture', 'comprehensive view'
            ],
            validation: [
                'verify', 'check', 'validate', 'confirm', 'fact-check', 'is it true',
                'accurate', 'reliable', 'trustworthy', 'evidence'
            ],
            exploration: [
                'explore', 'discover', 'brainstorm', 'possibilities', 'alternatives',
                'what if', 'potential', 'opportunities', 'new ideas'
            ],
            comparison: [
                'compare', 'versus', 'vs', 'difference', 'similar', 'better',
                'which is', 'choose between', 'options'
            ]
        };
        this.intentClassifier.set('patterns', intentPatterns);
    }
    async processMessage(userId, sessionId, message, context) {
        const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackAIOperation('process_message', 'cognitive_assistant');
        try {
            const conversationContext = await this.getOrCreateContext(userId, sessionId, context);
            const intent = await this.classifyIntent(message, conversationContext);
            const response = await this.generateContextualResponse(message, intent, conversationContext);
            const actions = await this.determineActions(intent, conversationContext);
            const recommendations = await this.generateRecommendations(intent, conversationContext);
            const turn = {
                id: `turn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                timestamp: new Date(),
                userMessage: message,
                assistantResponse: response,
                intent,
                actions,
                metadata: { sessionId, processingTime: Date.now() }
            };
            conversationContext.conversationHistory.push(turn);
            await this.updateLearningData(conversationContext, intent, actions);
            await this.cacheContext(conversationContext);
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
            this.emit('message_processed', {
                userId,
                sessionId,
                intent,
                actionsCount: actions.length,
                recommendationsCount: recommendations.length
            });
            return { response, actions, recommendations, intent };
        }
        catch (error) {
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Message processing failed');
            throw error;
        }
    }
    async classifyIntent(message, context) {
        const patterns = this.intentClassifier.get('patterns');
        const messageLower = message.toLowerCase();
        const intentScores = {};
        for (const [intentType, keywords] of Object.entries(patterns)) {
            let score = 0;
            keywords.forEach(keyword => {
                if (messageLower.includes(keyword)) {
                    score += keyword.length;
                }
            });
            intentScores[intentType] = score;
        }
        const topIntent = Object.entries(intentScores).reduce((a, b) => a[1] > b[1] ? a : b);
        const entities = this.extractEntities(message);
        const keywords = this.extractKeywords(message);
        const domain = await this.identifyDomain(message, context);
        const complexity = this.assessComplexity(message, context);
        const scope = this.determineScope(message);
        return {
            type: topIntent[0],
            confidence: Math.min(topIntent[1] * 10, 100),
            entities,
            keywords,
            domain,
            complexity,
            scope
        };
    }
    async generateContextualResponse(message, intent, context) {
        const promptContext = {
            domain: intent.domain,
            taskType: intent.type,
            userLevel: context.userProfile.preferredComplexity,
            language: 'english',
            tone: context.userProfile.communicationStyle === 'technical' ? 'technical' : 'friendly',
            outputFormat: 'text',
            constraints: [],
            requirements: [
                'Be helpful and informative',
                'Consider conversation history',
                'Adapt to user expertise level',
                'Provide actionable insights'
            ]
        };
        const recentHistory = context.conversationHistory.slice(-3);
        const historyContext = recentHistory.map(turn => `User: ${turn.userMessage}\nAssistant: ${turn.assistantResponse}`).join('\n\n');
        const variables = {
            user_message: message,
            intent_type: intent.type,
            domain: intent.domain,
            entities: intent.entities.join(', '),
            keywords: intent.keywords.join(', '),
            conversation_history: historyContext,
            user_expertise: context.userProfile.preferredComplexity,
            communication_style: context.userProfile.communicationStyle
        };
        const optimizedPrompt = await this.promptEngine.generateOptimizedPrompt(this.getBasePromptForIntent(intent.type), promptContext, variables);
        const aiResponse = await this.aiService.generateResponse(optimizedPrompt, {
            taskType: intent.type,
            priority: 'medium',
            requiresFactualAccuracy: intent.type === 'validation' || intent.type === 'research',
            requiresCreativity: intent.type === 'exploration',
            domain: intent.domain,
            userId: context.userId,
            sessionId: context.sessionId
        });
        return aiResponse.finalResponse;
    }
    async determineActions(intent, context) {
        const actions = [];
        if (intent.type === 'research' || intent.type === 'exploration') {
            actions.push({
                type: 'research',
                description: `Conduct ${intent.type} on ${intent.entities.join(', ')}`,
                parameters: {
                    entities: intent.entities,
                    keywords: intent.keywords,
                    domain: intent.domain,
                    complexity: intent.complexity
                },
                confidence: intent.confidence,
                timestamp: new Date()
            });
        }
        if (intent.type === 'analysis' || intent.type === 'comparison') {
            actions.push({
                type: 'analysis',
                description: `Perform ${intent.type} analysis`,
                parameters: {
                    analysisType: intent.type,
                    entities: intent.entities,
                    domain: intent.domain
                },
                confidence: intent.confidence,
                timestamp: new Date()
            });
        }
        actions.push({
            type: 'learning',
            description: 'Update user learning profile',
            parameters: {
                intent,
                userProfile: context.userProfile,
                conversationTurn: context.conversationHistory.length
            },
            confidence: 100,
            timestamp: new Date()
        });
        return actions;
    }
    async generateRecommendations(intent, context) {
        const recommendations = [];
        if (intent.scope === 'narrow' && intent.entities.length > 0) {
            recommendations.push({
                type: 'topic_expansion',
                title: 'Expand Research Scope',
                description: `Consider exploring related topics to ${intent.entities[0]}`,
                reasoning: 'Broader research often reveals valuable connections and insights',
                confidence: 75,
                priority: 'medium',
                estimatedValue: 80,
                actionable: true
            });
        }
        if (context.userProfile.preferredComplexity === 'beginner' && intent.complexity === 'complex') {
            recommendations.push({
                type: 'methodology_improvement',
                title: 'Simplify Research Approach',
                description: 'Break down complex topics into smaller, manageable parts',
                reasoning: 'Matches your current expertise level for better understanding',
                confidence: 85,
                priority: 'high',
                estimatedValue: 90,
                actionable: true
            });
        }
        if (intent.type === 'validation' || intent.type === 'research') {
            recommendations.push({
                type: 'source_suggestion',
                title: 'Diversify Information Sources',
                description: 'Include academic papers, expert opinions, and recent studies',
                reasoning: 'Multiple source types provide comprehensive perspective',
                confidence: 80,
                priority: 'medium',
                estimatedValue: 85,
                actionable: true
            });
        }
        return recommendations;
    }
    async updateLearningData(context, intent, actions) {
        const learningData = context.learningData;
        if (intent.confidence > 70) {
            learningData.successfulQueries.push(intent.entities.join(' '));
        }
        if (intent.complexity === 'complex' || intent.complexity === 'expert') {
            learningData.challengingTopics.push(intent.domain);
        }
        if (intent.domain in context.userProfile.expertise) {
            const currentLevel = context.userProfile.expertise[intent.domain];
            if (intent.confidence > 80 && actions.length > 0) {
                if (currentLevel === 'beginner') {
                    context.userProfile.expertise[intent.domain] = 'intermediate';
                }
                else if (currentLevel === 'intermediate') {
                    context.userProfile.expertise[intent.domain] = 'expert';
                }
            }
        }
        else {
            context.userProfile.expertise[intent.domain] = 'beginner';
        }
        learningData.lastLearningUpdate = new Date();
    }
    async getOrCreateContext(userId, sessionId, partialContext) {
        const contextKey = `${userId}_${sessionId}`;
        if (this.userContexts.has(contextKey)) {
            return this.userContexts.get(contextKey);
        }
        const cached = await EnhancedCacheService_1.enhancedCacheService.get(`context:${contextKey}`);
        if (cached) {
            this.userContexts.set(contextKey, cached);
            return cached;
        }
        const newContext = {
            userId,
            sessionId,
            conversationHistory: [],
            userProfile: {
                userId,
                expertise: {},
                interests: [],
                researchHistory: [],
                preferredComplexity: 'moderate',
                communicationStyle: 'conversational',
                learningGoals: [],
                lastActive: new Date()
            },
            preferences: {
                responseLength: 'medium',
                includeSourceLinks: true,
                explanationLevel: 'intermediate',
                visualizations: false,
                realTimeUpdates: true,
                collaborativeMode: false
            },
            learningData: {
                successfulQueries: [],
                challengingTopics: [],
                preferredSources: [],
                feedbackPatterns: {},
                improvementAreas: [],
                lastLearningUpdate: new Date()
            },
            ...partialContext
        };
        this.userContexts.set(contextKey, newContext);
        return newContext;
    }
    async cacheContext(context) {
        const contextKey = `${context.userId}_${context.sessionId}`;
        await EnhancedCacheService_1.enhancedCacheService.set(`context:${contextKey}`, context, {
            ttl: 7200,
            tags: ['user_contexts', context.userId]
        });
    }
    extractEntities(message) {
        const words = message.toLowerCase().split(/\s+/);
        const entities = [];
        const originalWords = message.split(/\s+/);
        originalWords.forEach(word => {
            if (word.length > 2 && /^[A-Z]/.test(word)) {
                entities.push(word);
            }
        });
        const quotedMatches = message.match(/"([^"]+)"/g);
        if (quotedMatches) {
            entities.push(...quotedMatches.map(match => match.replace(/"/g, '')));
        }
        return [...new Set(entities)];
    }
    extractKeywords(message) {
        const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should']);
        const words = message.toLowerCase()
            .replace(/[^\w\s]/g, '')
            .split(/\s+/)
            .filter(word => word.length > 2 && !stopWords.has(word));
        return [...new Set(words)];
    }
    async identifyDomain(message, context) {
        const domainKeywords = {
            health: ['health', 'medical', 'medicine', 'vitamin', 'supplement', 'nutrition', 'diet', 'wellness'],
            technology: ['technology', 'software', 'computer', 'AI', 'machine learning', 'programming', 'code'],
            science: ['research', 'study', 'experiment', 'scientific', 'biology', 'chemistry', 'physics'],
            business: ['business', 'marketing', 'finance', 'economics', 'management', 'strategy'],
            education: ['education', 'learning', 'teaching', 'school', 'university', 'course']
        };
        const messageLower = message.toLowerCase();
        let bestDomain = 'general';
        let bestScore = 0;
        for (const [domain, keywords] of Object.entries(domainKeywords)) {
            const score = keywords.reduce((sum, keyword) => {
                return sum + (messageLower.includes(keyword) ? 1 : 0);
            }, 0);
            if (score > bestScore) {
                bestScore = score;
                bestDomain = domain;
            }
        }
        return bestDomain;
    }
    assessComplexity(message, context) {
        const complexityIndicators = {
            simple: ['what is', 'simple', 'basic', 'easy', 'quick'],
            moderate: ['explain', 'how does', 'compare', 'analyze'],
            complex: ['comprehensive', 'detailed', 'in-depth', 'thorough', 'advanced'],
            expert: ['technical', 'scientific', 'research', 'methodology', 'statistical']
        };
        const messageLower = message.toLowerCase();
        let bestComplexity = 'moderate';
        let bestScore = 0;
        for (const [complexity, indicators] of Object.entries(complexityIndicators)) {
            const score = indicators.reduce((sum, indicator) => {
                return sum + (messageLower.includes(indicator) ? 1 : 0);
            }, 0);
            if (score > bestScore) {
                bestScore = score;
                bestComplexity = complexity;
            }
        }
        if (bestScore === 0) {
            return context.userProfile.preferredComplexity;
        }
        return bestComplexity;
    }
    determineScope(message) {
        const scopeIndicators = {
            narrow: ['specific', 'particular', 'exact', 'precise', 'focused'],
            broad: ['general', 'overview', 'broad', 'wide', 'extensive'],
            comprehensive: ['comprehensive', 'complete', 'thorough', 'all', 'everything']
        };
        const messageLower = message.toLowerCase();
        let bestScope = 'broad';
        let bestScore = 0;
        for (const [scope, indicators] of Object.entries(scopeIndicators)) {
            const score = indicators.reduce((sum, indicator) => {
                return sum + (messageLower.includes(indicator) ? 1 : 0);
            }, 0);
            if (score > bestScore) {
                bestScore = score;
                bestScope = scope;
            }
        }
        return bestScope;
    }
    getBasePromptForIntent(intentType) {
        const basePrompts = {
            research: 'You are a research assistant helping to find comprehensive information about {{entities}}. Consider the user\'s {{user_expertise}} level and provide {{communication_style}} explanations.',
            analysis: 'You are an analytical expert helping to examine and evaluate {{entities}}. Provide structured analysis considering {{domain}} context.',
            synthesis: 'You are a synthesis specialist helping to combine and integrate information about {{entities}}. Create coherent understanding from multiple perspectives.',
            validation: 'You are a fact-checking expert helping to verify information about {{entities}}. Provide evidence-based validation with source credibility assessment.',
            exploration: 'You are an exploration guide helping to discover new insights about {{entities}}. Encourage creative thinking and novel connections.',
            comparison: 'You are a comparison specialist helping to evaluate differences and similarities between {{entities}}. Provide balanced comparative analysis.'
        };
        return basePrompts[intentType] || basePrompts.research;
    }
    startLearningLoop() {
        setInterval(() => {
            this.analyzeLearningPatterns();
        }, 1800000);
    }
    async analyzeLearningPatterns() {
        try {
            for (const context of this.userContexts.values()) {
                const recentHistory = context.conversationHistory.slice(-10);
                if (recentHistory.length > 5) {
                    const avgConfidence = recentHistory.reduce((sum, turn) => sum + turn.intent.confidence, 0) / recentHistory.length;
                    if (avgConfidence > 80) {
                        context.learningData.improvementAreas.push('ready_for_advanced_topics');
                    }
                    else if (avgConfidence < 60) {
                        context.learningData.improvementAreas.push('needs_simplified_approach');
                    }
                }
            }
        }
        catch (error) {
            logger_1.logger.error('Learning pattern analysis failed:', error);
        }
    }
    async recordFeedback(userId, sessionId, turnId, feedback) {
        const contextKey = `${userId}_${sessionId}`;
        const context = this.userContexts.get(contextKey);
        if (context) {
            const turn = context.conversationHistory.find(t => t.id === turnId);
            if (turn) {
                turn.feedback = feedback;
                const domain = turn.intent.domain;
                if (!context.learningData.feedbackPatterns[domain]) {
                    context.learningData.feedbackPatterns[domain] = 0;
                }
                context.learningData.feedbackPatterns[domain] += feedback.rating;
                await this.cacheContext(context);
                this.emit('feedback_received', {
                    userId,
                    sessionId,
                    turnId,
                    feedback
                });
            }
        }
    }
    getUserProfile(userId, sessionId) {
        const contextKey = `${userId}_${sessionId}`;
        const context = this.userContexts.get(contextKey);
        return context ? context.userProfile : null;
    }
    getConversationHistory(userId, sessionId, limit = 10) {
        const contextKey = `${userId}_${sessionId}`;
        const context = this.userContexts.get(contextKey);
        return context ? context.conversationHistory.slice(-limit) : [];
    }
    getLearningInsights(userId, sessionId) {
        const contextKey = `${userId}_${sessionId}`;
        const context = this.userContexts.get(contextKey);
        if (!context)
            return null;
        return {
            expertise: context.userProfile.expertise,
            successfulQueries: context.learningData.successfulQueries.length,
            challengingTopics: context.learningData.challengingTopics,
            improvementAreas: context.learningData.improvementAreas,
            feedbackPatterns: context.learningData.feedbackPatterns,
            conversationCount: context.conversationHistory.length
        };
    }
}
exports.CognitiveResearchAssistant = CognitiveResearchAssistant;
exports.default = CognitiveResearchAssistant;
//# sourceMappingURL=CognitiveResearchAssistant.js.map