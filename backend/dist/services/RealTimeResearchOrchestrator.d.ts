import { EventEmitter } from 'events';
import { MCPResearchOrchestrator } from './MCPResearchOrchestrator';
import { ResearchAIService } from './ResearchAIService';
export interface ResearchTask {
    id: string;
    sessionId: string;
    type: 'search' | 'analysis' | 'synthesis' | 'validation' | 'enhancement';
    query: string;
    parameters: Record<string, any>;
    priority: 'low' | 'medium' | 'high' | 'urgent';
    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
    progress: number;
    startTime?: Date;
    endTime?: Date;
    results?: any;
    error?: string;
    dependencies: string[];
    estimatedDuration: number;
    actualDuration?: number;
}
export interface ResearchSession {
    id: string;
    userId: string;
    roomId?: string;
    title: string;
    description?: string;
    type: 'autonomous' | 'guided' | 'collaborative';
    status: 'initializing' | 'running' | 'paused' | 'completed' | 'failed';
    tasks: Map<string, ResearchTask>;
    taskQueue: string[];
    currentTask?: string;
    results: Map<string, any>;
    metadata: {
        totalTasks: number;
        completedTasks: number;
        failedTasks: number;
        estimatedCompletion: Date;
        actualCompletion?: Date;
        qualityScore?: number;
        confidence?: number;
    };
    settings: {
        maxConcurrentTasks: number;
        autoRetry: boolean;
        retryAttempts: number;
        timeoutMs: number;
        qualityThreshold: number;
        enableRealTimeUpdates: boolean;
    };
    createdAt: Date;
    updatedAt: Date;
}
export interface ResearchAgent {
    id: string;
    name: string;
    type: 'search' | 'analysis' | 'synthesis' | 'validation';
    status: 'idle' | 'busy' | 'error' | 'offline';
    capabilities: string[];
    currentTask?: string;
    performance: {
        tasksCompleted: number;
        averageTime: number;
        successRate: number;
        qualityScore: number;
    };
    lastActivity: Date;
}
export declare class RealTimeResearchOrchestrator extends EventEmitter {
    private sessions;
    private agents;
    private taskExecutors;
    private mcpOrchestrator;
    private aiService;
    private isRunning;
    constructor(mcpOrchestrator: MCPResearchOrchestrator, aiService: ResearchAIService);
    private initializeAgents;
    private startOrchestrator;
    private orchestratorLoop;
    createSession(params: {
        userId: string;
        roomId?: string;
        title: string;
        description?: string;
        type: ResearchSession['type'];
        query: string;
        settings?: Partial<ResearchSession['settings']>;
    }): Promise<string>;
    private generateInitialTasks;
    private processSession;
    private getReadyTasks;
    private startTask;
    private executeTask;
    private completeTask;
    private failTask;
    private completeSession;
    private findAvailableAgent;
    private calculateSessionProgress;
    private calculateSessionQuality;
    private calculateSessionConfidence;
    private updateAgentStatuses;
    private serializeSession;
    private serializeTask;
    getSession(sessionId: string): ResearchSession | null;
    getActiveSessions(): ResearchSession[];
    getBusyAgents(): ResearchAgent[];
    getQueuedTasks(): ResearchTask[];
    pauseSession(sessionId: string): Promise<void>;
    resumeSession(sessionId: string): Promise<void>;
    cancelSession(sessionId: string): Promise<void>;
    getAgents(): ResearchAgent[];
    getAgentPerformance(): Record<string, any>;
    shutdown(): void;
}
export default RealTimeResearchOrchestrator;
//# sourceMappingURL=RealTimeResearchOrchestrator.d.ts.map