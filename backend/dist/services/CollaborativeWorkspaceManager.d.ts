import { EventEmitter } from 'events';
export interface WorkspaceDocument {
    id: string;
    workspaceId: string;
    title: string;
    content: string;
    type: 'research_note' | 'analysis' | 'report' | 'hypothesis' | 'conclusion';
    version: number;
    createdBy: string;
    createdAt: Date;
    lastModifiedBy: string;
    lastModifiedAt: Date;
    collaborators: string[];
    isLocked: boolean;
    lockedBy?: string;
    lockedAt?: Date;
    tags: string[];
    metadata: Record<string, any>;
}
export interface DocumentEdit {
    id: string;
    documentId: string;
    userId: string;
    username: string;
    type: 'insert' | 'delete' | 'replace' | 'format';
    position: number;
    length?: number;
    content?: string;
    timestamp: Date;
    applied: boolean;
}
export interface WorkspaceComment {
    id: string;
    documentId: string;
    userId: string;
    username: string;
    content: string;
    position?: number;
    selection?: {
        start: number;
        end: number;
    };
    parentId?: string;
    resolved: boolean;
    resolvedBy?: string;
    resolvedAt?: Date;
    reactions: Map<string, string[]>;
    createdAt: Date;
    updatedAt: Date;
}
export interface CollaborativeWorkspace {
    id: string;
    name: string;
    description?: string;
    type: 'research' | 'analysis' | 'project' | 'experiment';
    ownerId: string;
    members: Map<string, WorkspaceMember>;
    documents: Map<string, WorkspaceDocument>;
    comments: Map<string, WorkspaceComment>;
    settings: {
        isPublic: boolean;
        allowGuestAccess: boolean;
        requireApproval: boolean;
        enableVersioning: boolean;
        enableComments: boolean;
        enableRealTimeEditing: boolean;
        autoSave: boolean;
        autoSaveInterval: number;
    };
    permissions: {
        read: string[];
        write: string[];
        admin: string[];
    };
    createdAt: Date;
    updatedAt: Date;
    lastActivity: Date;
}
export interface WorkspaceMember {
    userId: string;
    username: string;
    role: 'owner' | 'admin' | 'editor' | 'viewer' | 'guest';
    joinedAt: Date;
    lastSeen: Date;
    isOnline: boolean;
    currentDocument?: string;
    cursor?: {
        documentId: string;
        position: number;
    };
    permissions: {
        canRead: boolean;
        canWrite: boolean;
        canComment: boolean;
        canInvite: boolean;
        canManage: boolean;
    };
}
export interface WorkspaceActivity {
    id: string;
    workspaceId: string;
    userId: string;
    username: string;
    type: 'document_created' | 'document_edited' | 'document_deleted' | 'comment_added' | 'member_joined' | 'member_left';
    description: string;
    metadata: Record<string, any>;
    timestamp: Date;
}
export declare class CollaborativeWorkspaceManager extends EventEmitter {
    private workspaces;
    private documentEdits;
    private activeUsers;
    private userCursors;
    private documentLocks;
    private activities;
    constructor();
    createWorkspace(params: {
        name: string;
        description?: string;
        type: CollaborativeWorkspace['type'];
        ownerId: string;
        ownerUsername: string;
        settings?: Partial<CollaborativeWorkspace['settings']>;
    }): Promise<string>;
    joinWorkspace(workspaceId: string, userId: string, username: string): Promise<void>;
    leaveWorkspace(workspaceId: string, userId: string): Promise<void>;
    createDocument(params: {
        workspaceId: string;
        title: string;
        content?: string;
        type: WorkspaceDocument['type'];
        userId: string;
        username: string;
        tags?: string[];
    }): Promise<string>;
    editDocument(params: {
        documentId: string;
        userId: string;
        username: string;
        edit: Omit<DocumentEdit, 'id' | 'documentId' | 'userId' | 'username' | 'timestamp' | 'applied'>;
    }): Promise<void>;
    addComment(params: {
        documentId: string;
        userId: string;
        username: string;
        content: string;
        position?: number;
        selection?: {
            start: number;
            end: number;
        };
        parentId?: string;
    }): Promise<string>;
    updateCursor(workspaceId: string, userId: string, documentId: string, position: number): void;
    lockDocument(documentId: string, userId: string, durationMs?: number): Promise<void>;
    unlockDocument(documentId: string, userId: string): Promise<void>;
    private applyEdit;
    private findDocument;
    private saveDocument;
    private logActivity;
    private startCleanupTasks;
    private serializeWorkspace;
    private serializeMember;
    private serializeDocument;
    private serializeComment;
    private serializeEdit;
    getWorkspace(workspaceId: string): CollaborativeWorkspace | null;
    getUserWorkspaces(userId: string): CollaborativeWorkspace[];
    getWorkspaceActivities(workspaceId: string, limit?: number): WorkspaceActivity[];
    getDocumentEdits(documentId: string, limit?: number): DocumentEdit[];
    getActiveUsers(workspaceId: string): string[];
    getUserCursors(workspaceId: string): Map<string, any>;
}
export default CollaborativeWorkspaceManager;
//# sourceMappingURL=CollaborativeWorkspaceManager.d.ts.map