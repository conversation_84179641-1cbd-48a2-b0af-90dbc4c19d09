"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.enhancedCacheService = exports.EnhancedCacheService = void 0;
const redis_1 = require("redis");
const logger_1 = require("../utils/logger");
const environment_1 = require("../config/environment");
class EnhancedCacheService {
    redisClient;
    memoryCache;
    cacheStats;
    metrics;
    maxMemorySize;
    compressionThreshold;
    constructor() {
        this.memoryCache = new Map();
        this.metrics = [];
        this.maxMemorySize = 100 * 1024 * 1024;
        this.compressionThreshold = 1024;
        this.cacheStats = {
            hits: 0,
            misses: 0,
            hitRate: 0,
            totalKeys: 0,
            memoryUsage: 0,
            lastUpdated: new Date()
        };
        this.initializeRedisClient();
        this.startCacheCleanup();
    }
    async initializeRedisClient() {
        try {
            this.redisClient = (0, redis_1.createClient)({
                url: environment_1.config.redis.url,
                socket: {
                    connectTimeout: 5000,
                    lazyConnect: true,
                    reconnectStrategy: (retries) => Math.min(retries * 50, 500)
                },
                database: environment_1.config.redis.database || 0
            });
            this.redisClient.on('error', (err) => {
                logger_1.logger.error('Enhanced Redis Cache Error:', err);
            });
            this.redisClient.on('connect', () => {
                logger_1.logger.info('✅ Enhanced Redis Cache connected');
            });
            await this.redisClient.connect();
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize Enhanced Redis Cache:', error);
        }
    }
    async get(key, options) {
        const startTime = Date.now();
        let hit = false;
        let data = null;
        try {
            const memoryData = this.getFromMemory(key);
            if (memoryData !== null) {
                data = memoryData;
                hit = true;
                logger_1.logger.debug(`Cache hit (L1): ${key}`);
            }
            else {
                const redisData = await this.getFromRedis(key);
                if (redisData !== null) {
                    data = redisData;
                    hit = true;
                    this.setInMemory(key, data, options);
                    logger_1.logger.debug(`Cache hit (L2): ${key}`);
                }
            }
            if (hit) {
                this.cacheStats.hits++;
            }
            else {
                this.cacheStats.misses++;
            }
            this.updateCacheStats();
            this.recordMetric('get', key, hit, Date.now() - startTime);
            return data;
        }
        catch (error) {
            logger_1.logger.error('Cache get error:', error, { key });
            this.cacheStats.misses++;
            return null;
        }
    }
    async set(key, data, options = {}) {
        const startTime = Date.now();
        const dataSize = this.calculateDataSize(data);
        try {
            const ttl = options.ttl || 3600;
            const priority = options.priority || 'medium';
            await this.setInRedis(key, data, ttl, options);
            if (this.shouldCacheInMemory(dataSize, priority)) {
                this.setInMemory(key, data, options);
            }
            if (options.tags && options.tags.length > 0) {
                await this.addCacheTags(key, options.tags);
            }
            this.recordMetric('set', key, true, Date.now() - startTime, dataSize);
            logger_1.logger.debug(`Cache set: ${key} (${dataSize} bytes)`);
        }
        catch (error) {
            logger_1.logger.error('Cache set error:', error, { key });
        }
    }
    async delete(key) {
        const startTime = Date.now();
        try {
            this.memoryCache.delete(key);
            if (this.redisClient?.isReady) {
                await this.redisClient.del(key);
            }
            this.recordMetric('delete', key, true, Date.now() - startTime);
            logger_1.logger.debug(`Cache deleted: ${key}`);
        }
        catch (error) {
            logger_1.logger.error('Cache delete error:', error, { key });
        }
    }
    async invalidateByTags(tags) {
        const startTime = Date.now();
        try {
            for (const tag of tags) {
                const keys = await this.getKeysByTag(tag);
                for (const key of keys) {
                    await this.delete(key);
                }
                if (this.redisClient?.isReady) {
                    await this.redisClient.del(`tag:${tag}`);
                }
            }
            this.recordMetric('invalidate', `tags:${tags.join(',')}`, true, Date.now() - startTime);
            logger_1.logger.info(`Cache invalidated by tags: ${tags.join(', ')}`);
        }
        catch (error) {
            logger_1.logger.error('Cache invalidation error:', error, { tags });
        }
    }
    getCacheStats() {
        this.updateCacheStats();
        return { ...this.cacheStats };
    }
    getCacheMetrics(limit = 100) {
        return this.metrics.slice(-limit);
    }
    async warmCache(warmupData) {
        logger_1.logger.info(`Starting cache warmup with ${warmupData.length} items`);
        const promises = warmupData.map(async ({ key, data, options }) => {
            try {
                await this.set(key, data, { ...options, priority: 'high' });
            }
            catch (error) {
                logger_1.logger.warn(`Cache warmup failed for key: ${key}`, error);
            }
        });
        await Promise.allSettled(promises);
        logger_1.logger.info('Cache warmup completed');
    }
    async clear() {
        try {
            this.memoryCache.clear();
            if (this.redisClient?.isReady) {
                await this.redisClient.flushDb();
            }
            this.cacheStats = {
                hits: 0,
                misses: 0,
                hitRate: 0,
                totalKeys: 0,
                memoryUsage: 0,
                lastUpdated: new Date()
            };
            logger_1.logger.info('All caches cleared');
        }
        catch (error) {
            logger_1.logger.error('Cache clear error:', error);
        }
    }
    getFromMemory(key) {
        const cached = this.memoryCache.get(key);
        if (cached && cached.expires > Date.now()) {
            return cached.data;
        }
        else if (cached) {
            this.memoryCache.delete(key);
        }
        return null;
    }
    async getFromRedis(key) {
        if (!this.redisClient?.isReady)
            return null;
        try {
            const data = await this.redisClient.get(key);
            if (data) {
                return JSON.parse(data);
            }
        }
        catch (error) {
            logger_1.logger.warn('Redis get error:', error, { key });
        }
        return null;
    }
    setInMemory(key, data, options = {}) {
        const ttl = (options.ttl || 3600) * 1000;
        const priority = options.priority || 'medium';
        if (this.getMemoryUsage() > this.maxMemorySize) {
            this.evictLowPriorityItems();
        }
        this.memoryCache.set(key, {
            data,
            expires: Date.now() + ttl,
            priority
        });
    }
    async setInRedis(key, data, ttl, options = {}) {
        if (!this.redisClient?.isReady)
            return;
        try {
            let serializedData = JSON.stringify(data);
            if (options.compress && serializedData.length > this.compressionThreshold) {
                logger_1.logger.debug(`Compressing data for key: ${key}`);
            }
            await this.redisClient.setEx(key, ttl, serializedData);
        }
        catch (error) {
            logger_1.logger.warn('Redis set error:', error, { key });
        }
    }
    shouldCacheInMemory(dataSize, priority) {
        if (dataSize > 1024 * 1024)
            return false;
        if (priority === 'high')
            return true;
        if (priority === 'medium' && this.getMemoryUsage() < this.maxMemorySize * 0.8) {
            return true;
        }
        return false;
    }
    calculateDataSize(data) {
        return JSON.stringify(data).length;
    }
    getMemoryUsage() {
        let totalSize = 0;
        for (const [key, value] of this.memoryCache) {
            totalSize += key.length + this.calculateDataSize(value.data);
        }
        return totalSize;
    }
    evictLowPriorityItems() {
        const lowPriorityKeys = Array.from(this.memoryCache.entries())
            .filter(([_, value]) => value.priority === 'low')
            .map(([key]) => key);
        lowPriorityKeys.forEach(key => this.memoryCache.delete(key));
        if (lowPriorityKeys.length > 0) {
            logger_1.logger.debug(`Evicted ${lowPriorityKeys.length} low priority cache items`);
        }
    }
    async addCacheTags(key, tags) {
        if (!this.redisClient?.isReady)
            return;
        try {
            for (const tag of tags) {
                await this.redisClient.sAdd(`tag:${tag}`, key);
            }
        }
        catch (error) {
            logger_1.logger.warn('Failed to add cache tags:', error, { key, tags });
        }
    }
    async getKeysByTag(tag) {
        if (!this.redisClient?.isReady)
            return [];
        try {
            return await this.redisClient.sMembers(`tag:${tag}`);
        }
        catch (error) {
            logger_1.logger.warn('Failed to get keys by tag:', error, { tag });
            return [];
        }
    }
    updateCacheStats() {
        const total = this.cacheStats.hits + this.cacheStats.misses;
        this.cacheStats.hitRate = total > 0 ? (this.cacheStats.hits / total) * 100 : 0;
        this.cacheStats.totalKeys = this.memoryCache.size;
        this.cacheStats.memoryUsage = this.getMemoryUsage();
        this.cacheStats.lastUpdated = new Date();
    }
    recordMetric(operationType, key, hit, executionTime, dataSize) {
        this.metrics.push({
            operationType,
            key,
            hit,
            executionTime,
            dataSize,
            timestamp: new Date()
        });
        if (this.metrics.length > 1000) {
            this.metrics = this.metrics.slice(-1000);
        }
    }
    startCacheCleanup() {
        setInterval(() => {
            const now = Date.now();
            for (const [key, value] of this.memoryCache) {
                if (value.expires <= now) {
                    this.memoryCache.delete(key);
                }
            }
        }, 5 * 60 * 1000);
    }
    async shutdown() {
        try {
            if (this.redisClient?.isReady) {
                await this.redisClient.quit();
            }
            this.memoryCache.clear();
            logger_1.logger.info('Enhanced Cache Service shutdown completed');
        }
        catch (error) {
            logger_1.logger.error('Cache shutdown error:', error);
        }
    }
}
exports.EnhancedCacheService = EnhancedCacheService;
exports.enhancedCacheService = new EnhancedCacheService();
//# sourceMappingURL=EnhancedCacheService.js.map