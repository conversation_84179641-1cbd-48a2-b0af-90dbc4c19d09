{"version": 3, "file": "CognitiveResearchAssistant.d.ts", "sourceRoot": "", "sources": ["../../src/services/CognitiveResearchAssistant.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAItC,OAAO,iBAAiB,MAAM,qBAAqB,CAAC;AACpD,OAAO,uBAAuB,MAAM,2BAA2B,CAAC;AAChE,OAAO,uBAAuB,MAAM,2BAA2B,CAAC;AAEhE,MAAM,WAAW,UAAU;IACzB,IAAI,EAAE,UAAU,GAAG,UAAU,GAAG,WAAW,GAAG,YAAY,GAAG,aAAa,GAAG,YAAY,CAAC;IAC1F,UAAU,EAAE,MAAM,CAAC;IACnB,QAAQ,EAAE,MAAM,EAAE,CAAC;IACnB,QAAQ,EAAE,MAAM,EAAE,CAAC;IACnB,MAAM,EAAE,MAAM,CAAC;IACf,UAAU,EAAE,QAAQ,GAAG,UAAU,GAAG,SAAS,GAAG,QAAQ,CAAC;IACzD,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,KAAK,EAAE,QAAQ,GAAG,OAAO,GAAG,eAAe,CAAC;CAC7C;AAED,MAAM,WAAW,mBAAmB;IAClC,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,MAAM,CAAC;IAClB,mBAAmB,EAAE,gBAAgB,EAAE,CAAC;IACxC,WAAW,EAAE,WAAW,CAAC;IACzB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,cAAc,CAAC,EAAE,MAAM,EAAE,CAAC;IAC1B,WAAW,EAAE,eAAe,CAAC;IAC7B,YAAY,EAAE,YAAY,CAAC;CAC5B;AAED,MAAM,WAAW,gBAAgB;IAC/B,EAAE,EAAE,MAAM,CAAC;IACX,SAAS,EAAE,IAAI,CAAC;IAChB,WAAW,EAAE,MAAM,CAAC;IACpB,iBAAiB,EAAE,MAAM,CAAC;IAC1B,MAAM,EAAE,UAAU,CAAC;IACnB,OAAO,EAAE,eAAe,EAAE,CAAC;IAC3B,QAAQ,CAAC,EAAE,YAAY,CAAC;IACxB,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAC/B;AAED,MAAM,WAAW,WAAW;IAC1B,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,UAAU,GAAG,cAAc,GAAG,QAAQ,CAAC,CAAC;IAClE,SAAS,EAAE,MAAM,EAAE,CAAC;IACpB,eAAe,EAAE,MAAM,EAAE,CAAC;IAC1B,mBAAmB,EAAE,QAAQ,GAAG,UAAU,GAAG,SAAS,GAAG,QAAQ,CAAC;IAClE,kBAAkB,EAAE,SAAS,GAAG,UAAU,GAAG,WAAW,GAAG,gBAAgB,CAAC;IAC5E,aAAa,EAAE,MAAM,EAAE,CAAC;IACxB,UAAU,EAAE,IAAI,CAAC;CAClB;AAED,MAAM,WAAW,eAAe;IAC9B,cAAc,EAAE,OAAO,GAAG,QAAQ,GAAG,MAAM,CAAC;IAC5C,kBAAkB,EAAE,OAAO,CAAC;IAC5B,gBAAgB,EAAE,OAAO,GAAG,cAAc,GAAG,UAAU,CAAC;IACxD,cAAc,EAAE,OAAO,CAAC;IACxB,eAAe,EAAE,OAAO,CAAC;IACzB,iBAAiB,EAAE,OAAO,CAAC;CAC5B;AAED,MAAM,WAAW,YAAY;IAC3B,iBAAiB,EAAE,MAAM,EAAE,CAAC;IAC5B,iBAAiB,EAAE,MAAM,EAAE,CAAC;IAC5B,gBAAgB,EAAE,MAAM,EAAE,CAAC;IAC3B,gBAAgB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACzC,gBAAgB,EAAE,MAAM,EAAE,CAAC;IAC3B,kBAAkB,EAAE,IAAI,CAAC;CAC1B;AAED,MAAM,WAAW,eAAe;IAC9B,IAAI,EAAE,UAAU,GAAG,UAAU,GAAG,WAAW,GAAG,gBAAgB,GAAG,eAAe,GAAG,UAAU,CAAC;IAC9F,WAAW,EAAE,MAAM,CAAC;IACpB,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAChC,MAAM,CAAC,EAAE,GAAG,CAAC;IACb,UAAU,EAAE,MAAM,CAAC;IACnB,SAAS,EAAE,IAAI,CAAC;CACjB;AAED,MAAM,WAAW,YAAY;IAC3B,MAAM,EAAE,MAAM,CAAC;IACf,OAAO,EAAE,OAAO,CAAC;IACjB,QAAQ,EAAE,OAAO,CAAC;IAClB,QAAQ,EAAE,OAAO,CAAC;IAClB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,WAAW,CAAC,EAAE,MAAM,EAAE,CAAC;IACvB,SAAS,EAAE,IAAI,CAAC;CACjB;AAED,MAAM,WAAW,sBAAsB;IACrC,IAAI,EAAE,iBAAiB,GAAG,yBAAyB,GAAG,mBAAmB,GAAG,oBAAoB,CAAC;IACjG,KAAK,EAAE,MAAM,CAAC;IACd,WAAW,EAAE,MAAM,CAAC;IACpB,SAAS,EAAE,MAAM,CAAC;IAClB,UAAU,EAAE,MAAM,CAAC;IACnB,QAAQ,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC;IACpC,cAAc,EAAE,MAAM,CAAC;IACvB,UAAU,EAAE,OAAO,CAAC;CACrB;AAKD,qBAAa,0BAA2B,SAAQ,YAAY;IAC1D,OAAO,CAAC,SAAS,CAAoB;IACrC,OAAO,CAAC,YAAY,CAA0B;IAC9C,OAAO,CAAC,eAAe,CAA0B;IACjD,OAAO,CAAC,YAAY,CAA+C;IACnE,OAAO,CAAC,gBAAgB,CAA+B;IACvD,OAAO,CAAC,cAAc,CAA+B;gBAGnD,SAAS,EAAE,iBAAiB,EAC5B,YAAY,EAAE,uBAAuB,EACrC,eAAe,EAAE,uBAAuB;IAc1C,OAAO,CAAC,0BAA0B;IAkCrB,cAAc,CACzB,MAAM,EAAE,MAAM,EACd,SAAS,EAAE,MAAM,EACjB,OAAO,EAAE,MAAM,EACf,OAAO,CAAC,EAAE,OAAO,CAAC,mBAAmB,CAAC,GACrC,OAAO,CAAC;QACT,QAAQ,EAAE,MAAM,CAAC;QACjB,OAAO,EAAE,eAAe,EAAE,CAAC;QAC3B,eAAe,EAAE,sBAAsB,EAAE,CAAC;QAC1C,MAAM,EAAE,UAAU,CAAC;KACpB,CAAC;YA0DY,cAAc;YAyCd,0BAA0B;YA+D1B,gBAAgB;YAqDhB,uBAAuB;YAsDvB,kBAAkB;YAoClB,kBAAkB;YAwDlB,YAAY;IAQ1B,OAAO,CAAC,eAAe;IAsBvB,OAAO,CAAC,eAAe;YAYT,cAAc;IA4B5B,OAAO,CAAC,gBAAgB;IA+BxB,OAAO,CAAC,cAAc;IAyBtB,OAAO,CAAC,sBAAsB;IAa9B,OAAO,CAAC,iBAAiB;YAOX,uBAAuB;IAuBxB,cAAc,CACzB,MAAM,EAAE,MAAM,EACd,SAAS,EAAE,MAAM,EACjB,MAAM,EAAE,MAAM,EACd,QAAQ,EAAE,YAAY,GACrB,OAAO,CAAC,IAAI,CAAC;IA4BT,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,WAAW,GAAG,IAAI;IAMrE,sBAAsB,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,GAAE,MAAW,GAAG,gBAAgB,EAAE;IAMjG,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,GAAG;CAenE;AAED,eAAe,0BAA0B,CAAC"}