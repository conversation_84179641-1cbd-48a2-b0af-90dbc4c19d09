"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.databaseConnectionManager = exports.DatabaseConnectionManager = void 0;
const mongodb_1 = require("mongodb");
const redis_1 = require("redis");
const weaviate_ts_client_1 = __importDefault(require("weaviate-ts-client"));
const logger_1 = require("../utils/logger");
const environment_1 = require("../config/environment");
const PerformanceMonitoringService_1 = require("./PerformanceMonitoringService");
class DatabaseConnectionManager {
    neo4jDriver = null;
    mongoClient = null;
    redisClient = null;
    weaviateClient = null;
    neo4jSessions = new Set();
    connectionHealth;
    healthCheckInterval = null;
    constructor() {
        this.connectionHealth = this.initializeHealthStatus();
        this.startHealthMonitoring();
    }
    async initializeConnections() {
        logger_1.logger.info('Initializing database connections with advanced pooling...');
        try {
            await Promise.allSettled([
                this.initializeNeo4j(),
                this.initializeMongoDB(),
                this.initializeRedis(),
                this.initializeWeaviate()
            ]);
            logger_1.logger.info('✅ Database connection manager initialized');
        }
        catch (error) {
            logger_1.logger.error('❌ Failed to initialize database connections:', error);
            throw error;
        }
    }
    async initializeNeo4j() {
        const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackDatabaseQuery('connect', 'neo4j');
        try {
            const neo4j = await Promise.resolve().then(() => __importStar(require('neo4j-driver')));
            this.neo4jDriver = neo4j.driver(environment_1.config.neo4j.uri, neo4j.auth.basic(environment_1.config.neo4j.username, environment_1.config.neo4j.password), {
                maxConnectionLifetime: 3 * 60 * 60 * 1000,
                maxConnectionPoolSize: 50,
                connectionAcquisitionTimeout: 60000,
                disableLosslessIntegers: true,
                connectionTimeout: 20000,
                maxTransactionRetryTime: 15000,
                logging: {
                    level: environment_1.config.isDevelopment ? 'debug' : 'info',
                    logger: (level, message) => logger_1.logger.debug(`Neo4j ${level}: ${message}`)
                }
            });
            await this.neo4jDriver.verifyConnectivity();
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
            logger_1.logger.info('✅ Neo4j connection pool initialized');
        }
        catch (error) {
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Neo4j connection failed');
            logger_1.logger.error('❌ Neo4j connection failed:', error);
            throw error;
        }
    }
    async initializeMongoDB() {
        const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackDatabaseQuery('connect', 'mongodb');
        try {
            this.mongoClient = new mongodb_1.MongoClient(environment_1.config.mongodb.uri, {
                maxPoolSize: 50,
                minPoolSize: 5,
                maxIdleTimeMS: 30000,
                serverSelectionTimeoutMS: 5000,
                socketTimeoutMS: 45000,
                connectTimeoutMS: 10000,
                heartbeatFrequencyMS: 10000,
                retryWrites: true,
                retryReads: true,
                compressors: ['zlib'],
                zlibCompressionLevel: 6
            });
            await this.mongoClient.connect();
            await this.mongoClient.db('admin').command({ ping: 1 });
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
            logger_1.logger.info('✅ MongoDB connection pool initialized');
        }
        catch (error) {
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'MongoDB connection failed');
            logger_1.logger.error('❌ MongoDB connection failed:', error);
            throw error;
        }
    }
    async initializeRedis() {
        const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackDatabaseQuery('connect', 'redis');
        try {
            this.redisClient = (0, redis_1.createClient)({
                url: environment_1.config.redis.url,
                socket: {
                    connectTimeout: 10000,
                    lazyConnect: false,
                    keepAlive: 30000,
                    reconnectStrategy: (retries) => {
                        const delay = Math.min(retries * 50, 500);
                        logger_1.logger.info(`Redis reconnecting in ${delay}ms (attempt ${retries})`);
                        return delay;
                    }
                },
                database: environment_1.config.redis.database || 0,
                commandsQueueMaxLength: 1000,
                pingInterval: 30000
            });
            this.redisClient.on('error', (err) => {
                logger_1.logger.error('Redis error:', err);
                this.updateConnectionHealth('redis', 'critical', 0, Date.now());
            });
            this.redisClient.on('connect', () => {
                logger_1.logger.info('Redis connected');
                this.updateConnectionHealth('redis', 'healthy', 0, Date.now());
            });
            this.redisClient.on('ready', () => {
                logger_1.logger.info('Redis ready');
            });
            this.redisClient.on('reconnecting', () => {
                logger_1.logger.warn('Redis reconnecting...');
                this.updateConnectionHealth('redis', 'warning', 0, Date.now());
            });
            await this.redisClient.connect();
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
            logger_1.logger.info('✅ Redis connection initialized');
        }
        catch (error) {
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Redis connection failed');
            logger_1.logger.error('❌ Redis connection failed:', error);
            throw error;
        }
    }
    async initializeWeaviate() {
        const metricId = PerformanceMonitoringService_1.performanceMonitoringService.trackDatabaseQuery('connect', 'weaviate');
        try {
            this.weaviateClient = weaviate_ts_client_1.default.client({
                scheme: environment_1.config.weaviate.scheme,
                host: environment_1.config.weaviate.host,
                headers: environment_1.config.weaviate.apiKey ? {
                    'X-OpenAI-Api-Key': environment_1.config.weaviate.apiKey
                } : undefined
            });
            await this.weaviateClient.misc.liveChecker().do();
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, true);
            logger_1.logger.info('✅ Weaviate connection initialized');
        }
        catch (error) {
            PerformanceMonitoringService_1.performanceMonitoringService.endMetric(metricId, false, error instanceof Error ? error.message : 'Weaviate connection failed');
            logger_1.logger.warn('⚠️ Weaviate connection failed, continuing without vector database');
        }
    }
    async getNeo4jSession() {
        if (!this.neo4jDriver) {
            throw new Error('Neo4j driver not initialized');
        }
        const session = this.neo4jDriver.session({
            defaultAccessMode: 'WRITE',
            bookmarks: [],
            database: environment_1.config.neo4j.database
        });
        this.neo4jSessions.add(session);
        setTimeout(() => {
            this.closeNeo4jSession(session);
        }, 30 * 60 * 1000);
        return session;
    }
    async closeNeo4jSession(session) {
        try {
            await session.close();
            this.neo4jSessions.delete(session);
        }
        catch (error) {
            logger_1.logger.warn('Failed to close Neo4j session:', error);
        }
    }
    getMongoDatabase(dbName = environment_1.config.mongodb.database) {
        if (!this.mongoClient) {
            throw new Error('MongoDB client not initialized');
        }
        return this.mongoClient.db(dbName);
    }
    getRedisClient() {
        if (!this.redisClient) {
            throw new Error('Redis client not initialized');
        }
        return this.redisClient;
    }
    getWeaviateClient() {
        if (!this.weaviateClient) {
            throw new Error('Weaviate client not initialized');
        }
        return this.weaviateClient;
    }
    async getConnectionPoolStats() {
        const stats = {
            neo4j: {
                active: this.neo4jSessions.size,
                idle: 0,
                total: this.neo4jSessions.size,
                maxSize: 50
            },
            mongodb: {
                active: 0,
                idle: 0,
                total: 0,
                maxSize: 50
            },
            redis: {
                connected: this.redisClient?.isOpen || false,
                ready: this.redisClient?.isReady || false,
                reconnecting: false
            },
            weaviate: {
                connected: this.weaviateClient !== null,
                lastPing: this.connectionHealth.weaviate.lastCheck
            }
        };
        if (this.mongoClient) {
            try {
                const adminDb = this.mongoClient.db('admin');
                const serverStatus = await adminDb.command({ serverStatus: 1 });
                stats.mongodb.active = serverStatus.connections?.current || 0;
                stats.mongodb.total = serverStatus.connections?.current || 0;
            }
            catch (error) {
                logger_1.logger.warn('Failed to get MongoDB connection stats:', error);
            }
        }
        return stats;
    }
    getDatabaseHealth() {
        return { ...this.connectionHealth };
    }
    async performHealthChecks() {
        const checks = [
            this.checkNeo4jHealth(),
            this.checkMongoDBHealth(),
            this.checkRedisHealth(),
            this.checkWeaviateHealth()
        ];
        await Promise.allSettled(checks);
    }
    async shutdown() {
        logger_1.logger.info('Shutting down database connections...');
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
        }
        const shutdownPromises = [];
        if (this.neo4jDriver) {
            for (const session of this.neo4jSessions) {
                shutdownPromises.push(this.closeNeo4jSession(session));
            }
            shutdownPromises.push(this.neo4jDriver.close());
        }
        if (this.mongoClient) {
            shutdownPromises.push(this.mongoClient.close());
        }
        if (this.redisClient) {
            shutdownPromises.push(this.redisClient.quit());
        }
        await Promise.allSettled(shutdownPromises);
        logger_1.logger.info('✅ All database connections closed');
    }
    initializeHealthStatus() {
        const now = new Date();
        return {
            neo4j: { status: 'critical', responseTime: 0, errorRate: 0, lastCheck: now },
            mongodb: { status: 'critical', responseTime: 0, errorRate: 0, lastCheck: now },
            redis: { status: 'critical', responseTime: 0, errorRate: 0, lastCheck: now },
            weaviate: { status: 'critical', responseTime: 0, errorRate: 0, lastCheck: now }
        };
    }
    startHealthMonitoring() {
        this.healthCheckInterval = setInterval(async () => {
            try {
                await this.performHealthChecks();
            }
            catch (error) {
                logger_1.logger.error('Health check failed:', error);
            }
        }, 30000);
    }
    async checkNeo4jHealth() {
        const startTime = Date.now();
        try {
            if (this.neo4jDriver) {
                await this.neo4jDriver.verifyConnectivity();
                const responseTime = Date.now() - startTime;
                this.updateConnectionHealth('neo4j', 'healthy', responseTime, startTime);
            }
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            this.updateConnectionHealth('neo4j', 'critical', responseTime, startTime);
            logger_1.logger.warn('Neo4j health check failed:', error);
        }
    }
    async checkMongoDBHealth() {
        const startTime = Date.now();
        try {
            if (this.mongoClient) {
                await this.mongoClient.db('admin').command({ ping: 1 });
                const responseTime = Date.now() - startTime;
                this.updateConnectionHealth('mongodb', 'healthy', responseTime, startTime);
            }
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            this.updateConnectionHealth('mongodb', 'critical', responseTime, startTime);
            logger_1.logger.warn('MongoDB health check failed:', error);
        }
    }
    async checkRedisHealth() {
        const startTime = Date.now();
        try {
            if (this.redisClient?.isReady) {
                await this.redisClient.ping();
                const responseTime = Date.now() - startTime;
                this.updateConnectionHealth('redis', 'healthy', responseTime, startTime);
            }
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            this.updateConnectionHealth('redis', 'critical', responseTime, startTime);
            logger_1.logger.warn('Redis health check failed:', error);
        }
    }
    async checkWeaviateHealth() {
        const startTime = Date.now();
        try {
            if (this.weaviateClient) {
                await this.weaviateClient.misc.liveChecker().do();
                const responseTime = Date.now() - startTime;
                this.updateConnectionHealth('weaviate', 'healthy', responseTime, startTime);
            }
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            this.updateConnectionHealth('weaviate', 'critical', responseTime, startTime);
            logger_1.logger.warn('Weaviate health check failed:', error);
        }
    }
    updateConnectionHealth(database, status, responseTime, timestamp) {
        this.connectionHealth[database] = {
            status,
            responseTime,
            errorRate: status === 'critical' ? 100 : 0,
            lastCheck: new Date(timestamp)
        };
    }
}
exports.DatabaseConnectionManager = DatabaseConnectionManager;
exports.databaseConnectionManager = new DatabaseConnectionManager();
//# sourceMappingURL=DatabaseConnectionManager.js.map