/* Import Cosmic Design Systems */
@import './styles/cosmic-design-system.css';
@import './styles/neurotransmitter-design-system.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-gray-50 text-gray-900 antialiased;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }

  /* Focus styles */
  *:focus {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2;
  }

  /* Selection styles */
  ::selection {
    @apply bg-primary-100 text-primary-900;
  }
}

/* Component styles */
@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply bg-secondary-100 text-secondary-900 hover:bg-secondary-200 focus:ring-secondary-500;
  }

  .btn-success {
    @apply bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }

  .btn-warning {
    @apply bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }

  .btn-error {
    @apply bg-error-600 text-white hover:bg-error-700 focus:ring-error-500;
  }

  .btn-ghost {
    @apply text-gray-700 hover:bg-gray-100 focus:ring-gray-500;
  }

  .btn-outline {
    @apply border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
  }

  /* Input styles */
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500;
  }

  .input-error {
    @apply border-error-300 text-error-900 placeholder-error-300 focus:ring-error-500 focus:border-error-500;
  }

  /* Card styles */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200;
  }

  .card-hover {
    @apply transition-shadow hover:shadow-md;
  }

  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }

  .badge-secondary {
    @apply bg-secondary-100 text-secondary-800;
  }

  .badge-success {
    @apply bg-success-100 text-success-800;
  }

  .badge-warning {
    @apply bg-warning-100 text-warning-800;
  }

  .badge-error {
    @apply bg-error-100 text-error-800;
  }

  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }

  /* Graph visualization styles */
  .graph-container {
    @apply relative w-full h-full overflow-hidden bg-white rounded-lg border border-gray-200;
  }

  .graph-svg {
    @apply w-full h-full cursor-grab;
  }

  .graph-svg:active {
    @apply cursor-grabbing;
  }

  .graph-node {
    @apply cursor-pointer transition-all duration-200;
  }

  .graph-node:hover {
    @apply drop-shadow-lg;
  }

  .graph-node.selected {
    @apply ring-2 ring-primary-500 ring-offset-2;
  }

  .graph-link {
    @apply transition-all duration-200;
  }

  .graph-link:hover {
    @apply stroke-primary-500;
  }

  .graph-tooltip {
    @apply absolute z-50 px-3 py-2 text-sm bg-gray-900 text-white rounded-md shadow-lg pointer-events-none;
  }

  /* Sidebar styles */
  .sidebar {
    @apply fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out;
  }

  .sidebar-open {
    @apply translate-x-0;
  }

  .sidebar-closed {
    @apply -translate-x-full;
  }

  /* Modal styles */
  .modal-overlay {
    @apply fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4;
  }

  .modal-content {
    @apply bg-white rounded-lg shadow-xl max-w-lg w-full max-h-screen overflow-y-auto;
  }

  /* Search styles */
  .search-container {
    @apply relative;
  }

  .search-results {
    @apply absolute top-full left-0 right-0 z-40 mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto;
  }

  .search-result-item {
    @apply px-4 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0;
  }

  .search-result-item.selected {
    @apply bg-primary-50 text-primary-900;
  }

  /* Upload area styles */
  .upload-area {
    @apply border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors;
  }

  .upload-area.dragover {
    @apply border-primary-500 bg-primary-50;
  }

  /* Code block styles */
  .code-block {
    @apply bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto;
  }

  /* Prose styles for markdown */
  .prose-custom {
    @apply prose prose-gray max-w-none;
  }

  .prose-custom h1,
  .prose-custom h2,
  .prose-custom h3,
  .prose-custom h4,
  .prose-custom h5,
  .prose-custom h6 {
    @apply text-gray-900;
  }

  .prose-custom a {
    @apply text-primary-600 hover:text-primary-700;
  }

  .prose-custom code {
    @apply bg-gray-100 text-gray-900 px-1 py-0.5 rounded text-sm;
  }

  .prose-custom pre {
    @apply bg-gray-900 text-gray-100;
  }

  .prose-custom blockquote {
    @apply border-l-4 border-primary-500 bg-primary-50 text-primary-900;
  }
}

/* Utility styles */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }

  /* Glass morphism effect */
  .glass {
    @apply bg-white bg-opacity-80 backdrop-blur-sm border border-white border-opacity-20;
  }

  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }

  /* Custom shadows */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(14, 165, 233, 0.3);
  }

  .shadow-glow-success {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
  }

  .shadow-glow-error {
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  /* Layout utilities */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  body {
    @apply text-black bg-white;
  }

  .card {
    @apply border border-gray-400;
  }
}
