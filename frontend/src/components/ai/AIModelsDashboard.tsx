import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Brain,
  Zap,
  Clock,
  TrendingUp,
  DollarSign,
  Activity,
  CheckCircle,
  AlertCircle,
  BarChart3,
  Cpu,
  Globe,
  Home,
  Shield,
  Sparkles
} from 'lucide-react';

interface AIModel {
  id: string;
  name: string;
  provider: 'openai' | 'anthropic' | 'google' | 'local' | 'huggingface';
  type: 'chat' | 'completion' | 'embedding' | 'multimodal';
  capabilities: string[];
  maxTokens: number;
  costPerToken: number;
  responseTime: number;
  accuracy: number;
  reliability: number;
  specialties: string[];
  isAvailable: boolean;
  usage: {
    usageCount: number;
    errorCount: number;
    errorRate: number;
    lastUsed: Date;
  };
  performance: {
    averageQuality: number;
    averageConfidence: number;
    averageResponseTime: number;
    requestCount: number;
  };
}

interface ModelsSummary {
  totalModels: number;
  availableModels: number;
  totalRequests: number;
  averageQuality: number;
  averageResponseTime: number;
  totalCostSaved: number;
}

interface AIModelsDashboardProps {
  className?: string;
}

export const AIModelsDashboard: React.FC<AIModelsDashboardProps> = ({ className = '' }) => {
  const [models, setModels] = useState<AIModel[]>([]);
  const [summary, setSummary] = useState<ModelsSummary | null>(null);
  const [selectedModel, setSelectedModel] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  const fetchModelsData = async () => {
    try {
      const response = await fetch('/api/enhanced-ai/models');
      const data = await response.json();
      
      if (data.success) {
        setModels(data.data.models);
        setSummary(data.data.summary);
      }
    } catch (error) {
      console.error('Failed to fetch models data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchModelsData();
    
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(fetchModelsData, 30000);
    setRefreshInterval(interval);
    
    return () => {
      if (refreshInterval) clearInterval(refreshInterval);
    };
  }, []);

  const getProviderIcon = (provider: string) => {
    switch (provider) {
      case 'openai': return <Brain className="w-5 h-5 text-green-600" />;
      case 'anthropic': return <Shield className="w-5 h-5 text-blue-600" />;
      case 'google': return <Globe className="w-5 h-5 text-red-600" />;
      case 'local': return <Home className="w-5 h-5 text-purple-600" />;
      default: return <Cpu className="w-5 h-5 text-gray-600" />;
    }
  };

  const getProviderColor = (provider: string) => {
    switch (provider) {
      case 'openai': return 'from-green-500 to-emerald-600';
      case 'anthropic': return 'from-blue-500 to-indigo-600';
      case 'google': return 'from-red-500 to-pink-600';
      case 'local': return 'from-purple-500 to-violet-600';
      default: return 'from-gray-500 to-slate-600';
    }
  };

  const getQualityColor = (quality: number) => {
    if (quality >= 90) return 'text-green-600 bg-green-100';
    if (quality >= 80) return 'text-blue-600 bg-blue-100';
    if (quality >= 70) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const formatCost = (cost: number) => {
    if (cost === 0) return 'Free';
    return `$${(cost * 1000).toFixed(4)}/1K tokens`;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-gray-600">Loading AI models...</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl p-6 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">Available Models</p>
                <p className="text-2xl font-bold">{summary.availableModels}/{summary.totalModels}</p>
              </div>
              <Brain className="w-8 h-8 text-blue-200" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl p-6 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm">Total Requests</p>
                <p className="text-2xl font-bold">{formatNumber(summary.totalRequests)}</p>
              </div>
              <Activity className="w-8 h-8 text-green-200" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-gradient-to-r from-purple-500 to-violet-600 rounded-xl p-6 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm">Avg Quality</p>
                <p className="text-2xl font-bold">{summary.averageQuality.toFixed(1)}%</p>
              </div>
              <TrendingUp className="w-8 h-8 text-purple-200" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-gradient-to-r from-orange-500 to-red-600 rounded-xl p-6 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm">Cost Saved</p>
                <p className="text-2xl font-bold">${summary.totalCostSaved.toFixed(0)}</p>
              </div>
              <DollarSign className="w-8 h-8 text-orange-200" />
            </div>
          </motion.div>
        </div>
      )}

      {/* Models Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <AnimatePresence>
          {models.map((model, index) => (
            <motion.div
              key={model.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`bg-white rounded-xl shadow-lg border-2 transition-all duration-200 cursor-pointer ${
                selectedModel === model.id 
                  ? 'border-blue-500 shadow-xl' 
                  : 'border-gray-200 hover:border-gray-300 hover:shadow-xl'
              }`}
              onClick={() => setSelectedModel(selectedModel === model.id ? null : model.id)}
            >
              {/* Model Header */}
              <div className={`bg-gradient-to-r ${getProviderColor(model.provider)} rounded-t-xl p-4 text-white`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getProviderIcon(model.provider)}
                    <div>
                      <h3 className="font-semibold">{model.name}</h3>
                      <p className="text-sm opacity-90">{model.provider}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {model.isAvailable ? (
                      <CheckCircle className="w-5 h-5 text-green-300" />
                    ) : (
                      <AlertCircle className="w-5 h-5 text-red-300" />
                    )}
                  </div>
                </div>
              </div>

              {/* Model Stats */}
              <div className="p-4 space-y-4">
                {/* Performance Metrics */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getQualityColor(model.performance.averageQuality)}`}>
                      {model.performance.averageQuality.toFixed(1)}% Quality
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-1 text-sm text-gray-600">
                      <Clock className="w-4 h-4" />
                      <span>{model.performance.averageResponseTime}ms</span>
                    </div>
                  </div>
                </div>

                {/* Usage Stats */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Requests:</span>
                    <span className="ml-1 font-medium">{formatNumber(model.usage.usageCount)}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Error Rate:</span>
                    <span className="ml-1 font-medium">{model.usage.errorRate.toFixed(1)}%</span>
                  </div>
                </div>

                {/* Cost */}
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">Cost:</span>
                  <span className="text-sm font-medium">{formatCost(model.costPerToken)}</span>
                </div>

                {/* Capabilities */}
                <div>
                  <span className="text-sm text-gray-500 block mb-2">Capabilities:</span>
                  <div className="flex flex-wrap gap-1">
                    {model.capabilities.slice(0, 3).map((capability, idx) => (
                      <span
                        key={idx}
                        className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs"
                      >
                        {capability}
                      </span>
                    ))}
                    {model.capabilities.length > 3 && (
                      <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs">
                        +{model.capabilities.length - 3} more
                      </span>
                    )}
                  </div>
                </div>

                {/* Expanded Details */}
                <AnimatePresence>
                  {selectedModel === model.id && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="border-t border-gray-200 pt-4 space-y-3"
                    >
                      {/* Specialties */}
                      <div>
                        <span className="text-sm text-gray-500 block mb-2">Specialties:</span>
                        <div className="flex flex-wrap gap-1">
                          {model.specialties.map((specialty, idx) => (
                            <span
                              key={idx}
                              className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs"
                            >
                              {specialty.replace(/_/g, ' ')}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* Technical Details */}
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Max Tokens:</span>
                          <span className="ml-1 font-medium">{formatNumber(model.maxTokens)}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Accuracy:</span>
                          <span className="ml-1 font-medium">{model.accuracy}%</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Reliability:</span>
                          <span className="ml-1 font-medium">{model.reliability}%</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Type:</span>
                          <span className="ml-1 font-medium">{model.type}</span>
                        </div>
                      </div>

                      {/* Last Used */}
                      <div className="text-sm">
                        <span className="text-gray-500">Last Used:</span>
                        <span className="ml-1 font-medium">
                          {new Date(model.usage.lastUsed).toLocaleString()}
                        </span>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Real-time Status */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Real-time Performance</h3>
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>Live updates</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Average Response Time */}
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-3">
              <Clock className="w-8 h-8 text-white" />
            </div>
            <p className="text-2xl font-bold text-gray-900">{summary?.averageResponseTime.toFixed(0)}ms</p>
            <p className="text-sm text-gray-600">Avg Response Time</p>
          </div>

          {/* Quality Score */}
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-3">
              <TrendingUp className="w-8 h-8 text-white" />
            </div>
            <p className="text-2xl font-bold text-gray-900">{summary?.averageQuality.toFixed(1)}%</p>
            <p className="text-sm text-gray-600">Quality Score</p>
          </div>

          {/* Cost Efficiency */}
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-violet-600 rounded-full flex items-center justify-center mx-auto mb-3">
              <Sparkles className="w-8 h-8 text-white" />
            </div>
            <p className="text-2xl font-bold text-gray-900">${summary?.totalCostSaved.toFixed(0)}</p>
            <p className="text-sm text-gray-600">Cost Saved</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIModelsDashboard;
