import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Menu,
  X,
  Search,
  Upload,
  BarChart3,
  Settings,
  Info,
  Network,
  Bell,
  User,
  Brain,
  Microscope,
  Pill,
  Sparkles,
  ChevronDown,
  ChevronRight,
  Heart,
  Zap,
  Shield,
  Target,
} from 'lucide-react';

import Button from '@/components/atoms/Button';
import Badge from '@/components/atoms/Badge';

interface LayoutProps {
  children: React.ReactNode;
}

const navigation = [
  { name: 'Graph', href: '/graph', icon: Network },
  { name: 'Infinite Graph', href: '/infinite-graph', icon: Sparkles, badge: 'Demo' },
  { name: 'Supplements', href: '/supplements', icon: Pill, badge: 'New' },
  { name: 'Search', href: '/search', icon: Search },
  { name: 'Research', href: '/research', icon: Microscope, badge: 'AI' },
  { name: 'Upload', href: '/upload', icon: Upload },
  { name: 'Analytics', href: '/analytics', icon: BarChart3 },
  { name: '<PERSON><PERSON><PERSON>', href: '/settings', icon: Settings },
  { name: 'About', href: '/about', icon: Info },
];

const neuroNavigation = [
  { name: 'Neuroregulation', href: '/neuroregulation', icon: Brain, badge: 'Overview', color: '#8b5cf6' },
  { name: 'Neuromediators Hub', href: '/neuromediators', icon: Microscope, badge: 'Unified', color: '#6366f1' },
  { name: 'Serotonin', href: '/serotonin', icon: Heart, badge: 'Mood', color: '#3b82f6' },
  { name: 'Dopamine', href: '/dopamine', icon: Zap, badge: 'Energy', color: '#10b981' },
  { name: 'GABA', href: '/gaba', icon: Shield, badge: 'Calm', color: '#a855f7' },
  { name: 'Acetylcholine', href: '/acetylcholine', icon: Target, badge: 'Focus', color: '#f59e0b' },
  { name: 'Braverman Test', href: '/braverman-test', icon: Brain, badge: 'Assessment', color: '#ec4899' },
  { name: 'Swiss Herbal', href: '/swiss-herbal', icon: Pill, badge: 'Traditional', color: '#22c55e' },
];

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [neuroSectionOpen, setNeuroSectionOpen] = useState(false);
  const location = useLocation();

  const currentPage = navigation.find(item => item.href === location.pathname);

  const sidebarVariants = {
    open: {
      x: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30,
      },
    },
    closed: {
      x: '-100%',
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30,
      },
    },
  };

  const overlayVariants = {
    open: {
      opacity: 1,
      display: 'block',
    },
    closed: {
      opacity: 0,
      transitionEnd: {
        display: 'none',
      },
    },
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar overlay */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
            variants={overlayVariants}
            initial="closed"
            animate="open"
            exit="closed"
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.div
        className="fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 lg:translate-x-0"
        variants={sidebarVariants}
        initial="closed"
        animate={sidebarOpen ? 'open' : 'closed'}
      >
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
            <Link to="/" className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                <Network className="w-5 h-5 text-white" />
              </div>
              <span className="ml-3 text-xl font-bold gradient-text">
                Suplementor
              </span>
            </Link>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => {
              const isActive = location.pathname === item.href;
              const Icon = item.icon;

              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    isActive
                      ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-500'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                  onClick={() => setSidebarOpen(false)}
                >
                  <Icon className="w-5 h-5 mr-3" />
                  {item.name}
                  {item.name === 'Upload' && (
                    <Badge variant="primary" size="sm" className="ml-auto">
                      New
                    </Badge>
                  )}
                  {item.badge && (
                    <Badge variant="secondary" size="sm" className="ml-auto">
                      {item.badge}
                    </Badge>
                  )}
                </Link>
              );
            })}

            {/* Neuroscience Section */}
            <div className="pt-4 mt-4 border-t border-gray-200">
              <button
                onClick={() => setNeuroSectionOpen(!neuroSectionOpen)}
                className="flex items-center w-full px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors"
              >
                <Brain className="w-5 h-5 mr-3" />
                Neuroscience
                {neuroSectionOpen ? (
                  <ChevronDown className="w-4 h-4 ml-auto" />
                ) : (
                  <ChevronRight className="w-4 h-4 ml-auto" />
                )}
              </button>

              <AnimatePresence>
                {neuroSectionOpen && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.2 }}
                    className="ml-6 mt-2 space-y-1"
                  >
                    {neuroNavigation.map((item) => {
                      const isActive = location.pathname === item.href;
                      const Icon = item.icon;

                      return (
                        <Link
                          key={item.name}
                          to={item.href}
                          className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
                            isActive
                              ? 'text-white shadow-lg transform scale-105'
                              : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 hover:transform hover:scale-102'
                          }`}
                          style={isActive ? {
                            background: `linear-gradient(135deg, ${item.color}dd 0%, ${item.color} 100%)`,
                            boxShadow: `0 4px 14px 0 ${item.color}40`
                          } : {}}
                          onClick={() => setSidebarOpen(false)}
                        >
                          <div
                            className={`w-8 h-8 rounded-lg flex items-center justify-center mr-3 ${
                              isActive ? 'bg-white bg-opacity-20' : 'bg-gray-100'
                            }`}
                          >
                            <Icon
                              className="w-4 h-4"
                              style={{ color: isActive ? 'white' : item.color }}
                            />
                          </div>
                          <div className="flex-1">
                            <div className="font-medium">{item.name}</div>
                            {item.badge && (
                              <div className={`text-xs ${isActive ? 'text-white text-opacity-80' : 'text-gray-500'}`}>
                                {item.badge}
                              </div>
                            )}
                          </div>
                        </Link>
                      );
                    })}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </nav>

          {/* User section */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <User className="w-5 h-5 text-gray-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">Demo User</p>
                <p className="text-xs text-gray-500"><EMAIL></p>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main content */}
      <div className="lg:ml-64">
        {/* Top bar */}
        <header className="bg-white border-b border-gray-200 h-16">
          <div className="flex items-center justify-between h-full px-4 sm:px-6 lg:px-8">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden"
              >
                <Menu className="w-5 h-5" />
              </Button>
              <h1 className="ml-4 text-2xl font-bold text-gray-900 lg:ml-0">
                {currentPage?.name || 'Suplementor'}
              </h1>
            </div>

            <div className="flex items-center space-x-4">
              {/* Notifications */}
              <div className="relative">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setNotificationsOpen(!notificationsOpen)}
                  className="relative"
                >
                  <Bell className="w-5 h-5" />
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                </Button>

                {/* Notifications dropdown */}
                <AnimatePresence>
                  {notificationsOpen && (
                    <motion.div
                      className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                    >
                      <div className="p-4">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">
                          Notifications
                        </h3>
                        <div className="space-y-3">
                          <div className="p-3 bg-blue-50 rounded-lg">
                            <p className="text-sm font-medium text-blue-900">
                              New supplement data available
                            </p>
                            <p className="text-xs text-blue-700 mt-1">
                              5 minutes ago
                            </p>
                          </div>
                          <div className="p-3 bg-green-50 rounded-lg">
                            <p className="text-sm font-medium text-green-900">
                              Graph analysis completed
                            </p>
                            <p className="text-xs text-green-700 mt-1">
                              1 hour ago
                            </p>
                          </div>
                          <div className="p-3 bg-yellow-50 rounded-lg">
                            <p className="text-sm font-medium text-yellow-900">
                              System maintenance scheduled
                            </p>
                            <p className="text-xs text-yellow-700 mt-1">
                              2 hours ago
                            </p>
                          </div>
                        </div>
                        <div className="mt-4 pt-3 border-t border-gray-200">
                          <Button variant="ghost" size="sm" fullWidth>
                            View all notifications
                          </Button>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* System status */}
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-600 hidden sm:inline">
                  All systems operational
                </span>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1">
          {children}
        </main>
      </div>

      {/* Click outside to close notifications */}
      {notificationsOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setNotificationsOpen(false)}
        />
      )}
    </div>
  );
};

export default Layout;
